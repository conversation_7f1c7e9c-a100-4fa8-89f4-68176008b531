{"lockFileVersion": 13, "registryFileHashes": {"https://bcr.bazel.build/bazel_registry.json": "8a28e4aff06ee60aed2a8c281907fb8bcbf3b753c91fb5a5c57da3215d5b3497", "https://bcr.bazel.build/modules/abseil-cpp/20210324.2/MODULE.bazel": "7cd0312e064fde87c8d1cd79ba06c876bd23630c83466e9500321be55c96ace2", "https://bcr.bazel.build/modules/abseil-cpp/20211102.0/MODULE.bazel": "70390338f7a5106231d20620712f7cccb659cd0e9d073d1991c038eb9fc57589", "https://bcr.bazel.build/modules/abseil-cpp/20230125.1/MODULE.bazel": "89047429cb0207707b2dface14ba7f8df85273d484c2572755be4bab7ce9c3a0", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0.bcr.1/MODULE.bazel": "1c8cec495288dccd14fdae6e3f95f772c1c91857047a098fad772034264cc8cb", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0/MODULE.bazel": "d253ae36a8bd9ee3c5955384096ccb6baf16a1b1e93e858370da0a3b94f77c16", "https://bcr.bazel.build/modules/abseil-cpp/20230802.1/MODULE.bazel": "fa92e2eb41a04df73cdabeec37107316f7e5272650f81d6cc096418fe647b915", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/MODULE.bazel": "37bcdb4440fbb61df6a1c296ae01b327f19e9bb521f9b8e26ec854b6f97309ed", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/source.json": "9be551b8d4e3ef76875c0d744b5d6a504a27e3ae67bc6b28f46415fd2d2957da", "https://bcr.bazel.build/modules/apple_support/1.5.0/MODULE.bazel": "50341a62efbc483e8a2a6aec30994a58749bd7b885e18dd96aa8c33031e558ef", "https://bcr.bazel.build/modules/apple_support/1.5.0/source.json": "eb98a7627c0bc486b57f598ad8da50f6625d974c8f723e9ea71bd39f709c9862", "https://bcr.bazel.build/modules/bazel_features/1.1.0/MODULE.bazel": "cfd42ff3b815a5f39554d97182657f8c4b9719568eb7fded2b9135f084bf760b", "https://bcr.bazel.build/modules/bazel_features/1.1.1/MODULE.bazel": "27b8c79ef57efe08efccbd9dd6ef70d61b4798320b8d3c134fd571f78963dbcd", "https://bcr.bazel.build/modules/bazel_features/1.11.0/MODULE.bazel": "f9382337dd5a474c3b7d334c2f83e50b6eaedc284253334cf823044a26de03e8", "https://bcr.bazel.build/modules/bazel_features/1.17.0/MODULE.bazel": "039de32d21b816b47bd42c778e0454217e9c9caac4a3cf8e15c7231ee3ddee4d", "https://bcr.bazel.build/modules/bazel_features/1.18.0/MODULE.bazel": "1be0ae2557ab3a72a57aeb31b29be347bcdc5d2b1eb1e70f39e3851a7e97041a", "https://bcr.bazel.build/modules/bazel_features/1.19.0/MODULE.bazel": "59adcdf28230d220f0067b1f435b8537dd033bfff8db21335ef9217919c7fb58", "https://bcr.bazel.build/modules/bazel_features/1.28.0/MODULE.bazel": "4b4200e6cbf8fa335b2c3f43e1d6ef3e240319c33d43d60cc0fbd4b87ece299d", "https://bcr.bazel.build/modules/bazel_features/1.28.0/source.json": "16a3fc5b4483cb307643791f5a4b7365fa98d2e70da7c378cdbde55f0c0b32cf", "https://bcr.bazel.build/modules/bazel_features/1.4.1/MODULE.bazel": "e45b6bb2350aff3e442ae1111c555e27eac1d915e77775f6fdc4b351b758b5d7", "https://bcr.bazel.build/modules/bazel_features/1.9.0/MODULE.bazel": "885151d58d90d8d9c811eb75e3288c11f850e1d6b481a8c9f766adee4712358b", "https://bcr.bazel.build/modules/bazel_features/1.9.1/MODULE.bazel": "8f679097876a9b609ad1f60249c49d68bfab783dd9be012faf9d82547b14815a", "https://bcr.bazel.build/modules/bazel_jar_jar/0.1.7/MODULE.bazel": "d2736a1dbfd8f72befc532823b5112f2597a28064ce4aac280c65bee7d8830a0", "https://bcr.bazel.build/modules/bazel_jar_jar/0.1.7/source.json": "f894f62528821f749b8d57fb8fb737b13f6cfacde422c88019d4c48077ad0d96", "https://bcr.bazel.build/modules/bazel_skylib/1.0.3/MODULE.bazel": "bcb0fd896384802d1ad283b4e4eb4d718eebd8cb820b0a2c3a347fb971afd9d8", "https://bcr.bazel.build/modules/bazel_skylib/1.1.1/MODULE.bazel": "1add3e7d93ff2e6998f9e118022c84d163917d912f5afafb3058e3d2f1545b5e", "https://bcr.bazel.build/modules/bazel_skylib/1.2.0/MODULE.bazel": "44fe84260e454ed94ad326352a698422dbe372b21a1ac9f3eab76eb531223686", "https://bcr.bazel.build/modules/bazel_skylib/1.2.1/MODULE.bazel": "f35baf9da0efe45fa3da1696ae906eea3d615ad41e2e3def4aeb4e8bc0ef9a7a", "https://bcr.bazel.build/modules/bazel_skylib/1.3.0/MODULE.bazel": "20228b92868bf5cfc41bda7afc8a8ba2a543201851de39d990ec957b513579c5", "https://bcr.bazel.build/modules/bazel_skylib/1.4.1/MODULE.bazel": "a0dcb779424be33100dcae821e9e27e4f2901d9dfd5333efe5ac6a8d7ab75e1d", "https://bcr.bazel.build/modules/bazel_skylib/1.4.2/MODULE.bazel": "3bd40978e7a1fac911d5989e6b09d8f64921865a45822d8b09e815eaa726a651", "https://bcr.bazel.build/modules/bazel_skylib/1.5.0/MODULE.bazel": "32880f5e2945ce6a03d1fbd588e9198c0a959bb42297b2cfaf1685b7bc32e138", "https://bcr.bazel.build/modules/bazel_skylib/1.6.1/MODULE.bazel": "8fdee2dbaace6c252131c00e1de4b165dc65af02ea278476187765e1a617b917", "https://bcr.bazel.build/modules/bazel_skylib/1.7.0/MODULE.bazel": "0db596f4563de7938de764cc8deeabec291f55e8ec15299718b93c4423e9796d", "https://bcr.bazel.build/modules/bazel_skylib/1.7.1/MODULE.bazel": "3120d80c5861aa616222ec015332e5f8d3171e062e3e804a2a0253e1be26e59b", "https://bcr.bazel.build/modules/bazel_skylib/1.8.1/MODULE.bazel": "88ade7293becda963e0e3ea33e7d54d3425127e0a326e0d17da085a5f1f03ff6", "https://bcr.bazel.build/modules/bazel_skylib/1.8.1/source.json": "7ebaefba0b03efe59cac88ed5bbc67bcf59a3eff33af937345ede2a38b2d368a", "https://bcr.bazel.build/modules/boringssl/0.0.0-20211025-d4f1ab9/MODULE.bazel": "6ee6353f8b1a701fe2178e1d925034294971350b6d3ac37e67e5a7d463267834", "https://bcr.bazel.build/modules/boringssl/0.0.0-20211025-d4f1ab9/source.json": "323bafff99739f6aba35b69a84f0bc04ddb4540a46c1694355f60f073dff3001", "https://bcr.bazel.build/modules/buildozer/7.1.2/MODULE.bazel": "2e8dd40ede9c454042645fd8d8d0cd1527966aa5c919de86661e62953cd73d84", "https://bcr.bazel.build/modules/buildozer/7.1.2/source.json": "c9028a501d2db85793a6996205c8de120944f50a0d570438fcae0457a5f9d1f8", "https://bcr.bazel.build/modules/c-ares/1.15.0/MODULE.bazel": "ba0a78360fdc83f02f437a9e7df0532ad1fbaa59b722f6e715c11effebaa0166", "https://bcr.bazel.build/modules/c-ares/1.15.0/source.json": "5e3ed991616c5ec4cc09b0893b29a19232de4a1830eb78c567121bfea87453f7", "https://bcr.bazel.build/modules/gazelle/0.32.0/MODULE.bazel": "b499f58a5d0d3537f3cf5b76d8ada18242f64ec474d8391247438bf04f58c7b8", "https://bcr.bazel.build/modules/gazelle/0.33.0/MODULE.bazel": "a13a0f279b462b784fb8dd52a4074526c4a2afe70e114c7d09066097a46b3350", "https://bcr.bazel.build/modules/gazelle/0.34.0/MODULE.bazel": "abdd8ce4d70978933209db92e436deb3a8b737859e9354fb5fd11fb5c2004c8a", "https://bcr.bazel.build/modules/gazelle/0.36.0/MODULE.bazel": "e375d5d6e9a6ca59b0cb38b0540bc9a05b6aa926d322f2de268ad267a2ee74c0", "https://bcr.bazel.build/modules/gazelle/0.37.0/MODULE.bazel": "d1327ba0907d0275ed5103bfbbb13518f6c04955b402213319d0d6c0ce9839d4", "https://bcr.bazel.build/modules/gazelle/0.43.0/MODULE.bazel": "846e1fe396eefc0f9ddad2b33e9bd364dd993fc2f42a88e31590fe0b0eefa3f0", "https://bcr.bazel.build/modules/gazelle/0.43.0/source.json": "021a77f6625906d9d176e2fa351175e842622a5d45989312f2ad4924aab72df6", "https://bcr.bazel.build/modules/google_benchmark/1.8.2/MODULE.bazel": "a70cf1bba851000ba93b58ae2f6d76490a9feb74192e57ab8e8ff13c34ec50cb", "https://bcr.bazel.build/modules/googletest/1.11.0/MODULE.bazel": "3a83f095183f66345ca86aa13c58b59f9f94a2f81999c093d4eeaa2d262d12f4", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/MODULE.bazel": "22c31a561553727960057361aa33bf20fb2e98584bc4fec007906e27053f80c6", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/source.json": "41e9e129f80d8c8bf103a7acc337b76e54fad1214ac0a7084bf24f4cd924b8b4", "https://bcr.bazel.build/modules/googletest/1.14.0/MODULE.bazel": "cfbcbf3e6eac06ef9d85900f64424708cc08687d1b527f0ef65aa7517af8118f", "https://bcr.bazel.build/modules/grpc/1.41.0/MODULE.bazel": "5bcbfc2b274dabea628f0649dc50c90cf36543b1cfc31624832538644ad1aae8", "https://bcr.bazel.build/modules/grpc/1.41.0/source.json": "95459000d92171f5a5d75930577d8bc09baa81d11d02893aae104dc11c13831e", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/MODULE.bazel": "31271aedc59e815656f5736f282bb7509a97c7ecb43e927ac1a37966e0578075", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/source.json": "4108ee5085dd2885a341c7fab149429db457b3169b86eb081fa245eadf69169d", "https://bcr.bazel.build/modules/libpfm/4.11.0/MODULE.bazel": "45061ff025b301940f1e30d2c16bea596c25b176c8b6b3087e92615adbd52902", "https://bcr.bazel.build/modules/openapi_tools_generator_bazel/0.2.0/MODULE.bazel": "b6af887b3d7602b09b8bdea92ea2db00a86a250d0586e11057a9ff709c22a396", "https://bcr.bazel.build/modules/openapi_tools_generator_bazel/0.2.0/source.json": "8b00ec2c5ea3cd2f9f123fa927a0984617f879f61ea5d0396b28b99d32f925c2", "https://bcr.bazel.build/modules/opencensus-proto/0.4.1/MODULE.bazel": "4a2e8b4d0b544002502474d611a5a183aa282251e14f6a01afe841c0c1b10372", "https://bcr.bazel.build/modules/opencensus-proto/0.4.1/source.json": "a7d956700a85b833c43fc61455c0e111ab75bab40768ed17a206ee18a2bbe38f", "https://bcr.bazel.build/modules/platforms/0.0.10/MODULE.bazel": "8cb8efaf200bdeb2150d93e162c40f388529a25852b332cec879373771e48ed5", "https://bcr.bazel.build/modules/platforms/0.0.11/MODULE.bazel": "0daefc49732e227caa8bfa834d65dc52e8cc18a2faf80df25e8caea151a9413f", "https://bcr.bazel.build/modules/platforms/0.0.4/MODULE.bazel": "9b328e31ee156f53f3c416a64f8491f7eb731742655a47c9eec4703a71644aee", "https://bcr.bazel.build/modules/platforms/0.0.5/MODULE.bazel": "5733b54ea419d5eaf7997054bb55f6a1d0b5ff8aedf0176fef9eea44f3acda37", "https://bcr.bazel.build/modules/platforms/0.0.6/MODULE.bazel": "ad6eeef431dc52aefd2d77ed20a4b353f8ebf0f4ecdd26a807d2da5aa8cd0615", "https://bcr.bazel.build/modules/platforms/0.0.7/MODULE.bazel": "72fd4a0ede9ee5c021f6a8dd92b503e089f46c227ba2813ff183b71616034814", "https://bcr.bazel.build/modules/platforms/0.0.8/MODULE.bazel": "9f142c03e348f6d263719f5074b21ef3adf0b139ee4c5133e2aa35664da9eb2d", "https://bcr.bazel.build/modules/platforms/0.0.9/MODULE.bazel": "4a87a60c927b56ddd67db50c89acaa62f4ce2a1d2149ccb63ffd871d5ce29ebc", "https://bcr.bazel.build/modules/platforms/1.0.0/MODULE.bazel": "f05feb42b48f1b3c225e4ccf351f367be0371411a803198ec34a389fb22aa580", "https://bcr.bazel.build/modules/platforms/1.0.0/source.json": "f4ff1fd412e0246fd38c82328eb209130ead81d62dcd5a9e40910f867f733d96", "https://bcr.bazel.build/modules/protobuf/21.7/MODULE.bazel": "a5a29bb89544f9b97edce05642fac225a808b5b7be74038ea3640fae2f8e66a7", "https://bcr.bazel.build/modules/protobuf/27.0/MODULE.bazel": "7873b60be88844a0a1d8f80b9d5d20cfbd8495a689b8763e76c6372998d3f64c", "https://bcr.bazel.build/modules/protobuf/29.0/MODULE.bazel": "319dc8bf4c679ff87e71b1ccfb5a6e90a6dbc4693501d471f48662ac46d04e4e", "https://bcr.bazel.build/modules/protobuf/29.1/MODULE.bazel": "557c3457560ff49e122ed76c0bc3397a64af9574691cb8201b4e46d4ab2ecb95", "https://bcr.bazel.build/modules/protobuf/29.1/source.json": "04cca85dce26b895ed037d98336d860367fe09919208f2ad383f0df1aff63199", "https://bcr.bazel.build/modules/protobuf/3.19.0/MODULE.bazel": "6b5fbb433f760a99a22b18b6850ed5784ef0e9928a72668b66e4d7ccd47db9b0", "https://bcr.bazel.build/modules/protobuf/3.19.2/MODULE.bazel": "532ffe5f2186b69fdde039efe6df13ba726ff338c6bc82275ad433013fa10573", "https://bcr.bazel.build/modules/protobuf/3.19.6/MODULE.bazel": "9233edc5e1f2ee276a60de3eaa47ac4132302ef9643238f23128fea53ea12858", "https://bcr.bazel.build/modules/protoc-gen-validate/1.2.1/MODULE.bazel": "52b51f50533ec4fbd5d613cd093773f979ac2e035d954e02ca11de383f502505", "https://bcr.bazel.build/modules/protoc-gen-validate/1.2.1/source.json": "5fbbe2eec1a6ae89f5454365d91eeb75584e3f96ac570290e6ba33ebe32774df", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/MODULE.bazel": "88af1c246226d87e65be78ed49ecd1e6f5e98648558c14ce99176da041dc378e", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/source.json": "be4789e951dd5301282729fe3d4938995dc4c1a81c2ff150afc9f1b0504c6022", "https://bcr.bazel.build/modules/re2/2021-09-01/MODULE.bazel": "bcb6b96f3b071e6fe2d8bed9cc8ada137a105f9d2c5912e91d27528b3d123833", "https://bcr.bazel.build/modules/re2/2023-09-01/MODULE.bazel": "cb3d511531b16cfc78a225a9e2136007a48cf8a677e4264baeab57fe78a80206", "https://bcr.bazel.build/modules/re2/2023-09-01/source.json": "e044ce89c2883cd957a2969a43e79f7752f9656f6b20050b62f90ede21ec6eb4", "https://bcr.bazel.build/modules/rules_cc/0.0.1/MODULE.bazel": "cb2aa0747f84c6c3a78dad4e2049c154f08ab9d166b1273835a8174940365647", "https://bcr.bazel.build/modules/rules_cc/0.0.10/MODULE.bazel": "ec1705118f7eaedd6e118508d3d26deba2a4e76476ada7e0e3965211be012002", "https://bcr.bazel.build/modules/rules_cc/0.0.15/MODULE.bazel": "6704c35f7b4a72502ee81f61bf88706b54f06b3cbe5558ac17e2e14666cd5dcc", "https://bcr.bazel.build/modules/rules_cc/0.0.16/MODULE.bazel": "7661303b8fc1b4d7f532e54e9d6565771fea666fbdf839e0a86affcd02defe87", "https://bcr.bazel.build/modules/rules_cc/0.0.2/MODULE.bazel": "6915987c90970493ab97393024c156ea8fb9f3bea953b2f3ec05c34f19b5695c", "https://bcr.bazel.build/modules/rules_cc/0.0.6/MODULE.bazel": "abf360251023dfe3efcef65ab9d56beefa8394d4176dd29529750e1c57eaa33f", "https://bcr.bazel.build/modules/rules_cc/0.0.8/MODULE.bazel": "964c85c82cfeb6f3855e6a07054fdb159aced38e99a5eecf7bce9d53990afa3e", "https://bcr.bazel.build/modules/rules_cc/0.0.9/MODULE.bazel": "836e76439f354b89afe6a911a7adf59a6b2518fafb174483ad78a2a2fde7b1c5", "https://bcr.bazel.build/modules/rules_cc/0.1.4/MODULE.bazel": "bb03a452a7527ac25a7518fb86a946ef63df860b9657d8323a0c50f8504fb0b9", "https://bcr.bazel.build/modules/rules_cc/0.1.4/source.json": "f8e6678214b318585ed9f8deb55ae64ba7708465e4f201f41ab636b9c63851e1", "https://bcr.bazel.build/modules/rules_foreign_cc/0.9.0/MODULE.bazel": "c9e8c682bf75b0e7c704166d79b599f93b72cfca5ad7477df596947891feeef6", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/MODULE.bazel": "40c97d1144356f52905566c55811f13b299453a14ac7769dfba2ac38192337a8", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/source.json": "c8b1e2c717646f1702290959a3302a178fb639d987ab61d548105019f11e527e", "https://bcr.bazel.build/modules/rules_go/0.41.0/MODULE.bazel": "55861d8e8bb0e62cbd2896f60ff303f62ffcb0eddb74ecb0e5c0cbe36fc292c8", "https://bcr.bazel.build/modules/rules_go/0.42.0/MODULE.bazel": "8cfa875b9aa8c6fce2b2e5925e73c1388173ea3c32a0db4d2b4804b453c14270", "https://bcr.bazel.build/modules/rules_go/0.45.1/MODULE.bazel": "6d7884f0edf890024eba8ab31a621faa98714df0ec9d512389519f0edff0281a", "https://bcr.bazel.build/modules/rules_go/0.46.0/MODULE.bazel": "3477df8bdcc49e698b9d25f734c4f3a9f5931ff34ee48a2c662be168f5f2d3fd", "https://bcr.bazel.build/modules/rules_go/0.48.0/MODULE.bazel": "d00ebcae0908ee3f5e6d53f68677a303d6d59a77beef879598700049c3980a03", "https://bcr.bazel.build/modules/rules_go/0.50.1/MODULE.bazel": "b91a308dc5782bb0a8021ad4330c81fea5bda77f96b9e4c117b9b9c8f6665ee0", "https://bcr.bazel.build/modules/rules_go/0.50.1/source.json": "205765fd30216c70321f84c9a967267684bdc74350af3f3c46c857d9f80a4fa2", "https://bcr.bazel.build/modules/rules_helm/0.14.0/MODULE.bazel": "22bd58ab9e7fd86a06e08f0c2a7ce8c928fbcbe319aab031ff2f4b41e3577c1d", "https://bcr.bazel.build/modules/rules_helm/0.14.0/source.json": "962d58ef23a0d93d65eed6691814ae7370b60575afc97c9355c4ec938f75e4d8", "https://bcr.bazel.build/modules/rules_java/4.0.0/MODULE.bazel": "5a78a7ae82cd1a33cef56dc578c7d2a46ed0dca12643ee45edbb8417899e6f74", "https://bcr.bazel.build/modules/rules_java/5.3.5/MODULE.bazel": "a4ec4f2db570171e3e5eb753276ee4b389bae16b96207e9d3230895c99644b86", "https://bcr.bazel.build/modules/rules_java/5.5.0/MODULE.bazel": "486ad1aa15cdc881af632b4b1448b0136c76025a1fe1ad1b65c5899376b83a50", "https://bcr.bazel.build/modules/rules_java/6.0.0/MODULE.bazel": "8a43b7df601a7ec1af61d79345c17b31ea1fedc6711fd4abfd013ea612978e39", "https://bcr.bazel.build/modules/rules_java/6.3.1/MODULE.bazel": "5a3471c8b84d53d58d5f6e316313680d7dd2c70afac696dbe14b761b0b5c6a06", "https://bcr.bazel.build/modules/rules_java/6.4.0/MODULE.bazel": "e986a9fe25aeaa84ac17ca093ef13a4637f6107375f64667a15999f77db6c8f6", "https://bcr.bazel.build/modules/rules_java/6.5.2/MODULE.bazel": "1d440d262d0e08453fa0c4d8f699ba81609ed0e9a9a0f02cd10b3e7942e61e31", "https://bcr.bazel.build/modules/rules_java/7.12.2/MODULE.bazel": "579c505165ee757a4280ef83cda0150eea193eed3bef50b1004ba88b99da6de6", "https://bcr.bazel.build/modules/rules_java/7.12.5/MODULE.bazel": "e206a19dba0bbce27f62e35e36b6178aed9c52c58f9e30d56aa058cd059f34b7", "https://bcr.bazel.build/modules/rules_java/7.12.5/source.json": "7cbf2b5130a48b864221038f86ff94084ac398b812474d622a2d4bc5da9945cd", "https://bcr.bazel.build/modules/rules_java/7.2.0/MODULE.bazel": "06c0334c9be61e6cef2c8c84a7800cef502063269a5af25ceb100b192453d4ab", "https://bcr.bazel.build/modules/rules_java/7.3.2/MODULE.bazel": "50dece891cfdf1741ea230d001aa9c14398062f2b7c066470accace78e412bc2", "https://bcr.bazel.build/modules/rules_java/7.6.1/MODULE.bazel": "2f14b7e8a1aa2f67ae92bc69d1ec0fa8d9f827c4e17ff5e5f02e91caa3b2d0fe", "https://bcr.bazel.build/modules/rules_java/7.6.5/MODULE.bazel": "481164be5e02e4cab6e77a36927683263be56b7e36fef918b458d7a8a1ebadb1", "https://bcr.bazel.build/modules/rules_jvm_external/6.0/MODULE.bazel": "37c93a5a78d32e895d52f86a8d0416176e915daabd029ccb5594db422e87c495", "https://bcr.bazel.build/modules/rules_jvm_external/6.0/source.json": "73cc8818203a182e7374adf137f428d276190b2e2bef3022c231990cf0e594aa", "https://bcr.bazel.build/modules/rules_kotlin/1.9.0/MODULE.bazel": "ef85697305025e5a61f395d4eaede272a5393cee479ace6686dba707de804d59", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/MODULE.bazel": "d269a01a18ee74d0335450b10f62c9ed81f2321d7958a2934e44272fe82dcef3", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/source.json": "2faa4794364282db7c06600b7e5e34867a564ae91bda7cae7c29c64e9466b7d5", "https://bcr.bazel.build/modules/rules_license/0.0.3/MODULE.bazel": "627e9ab0247f7d1e05736b59dbb1b6871373de5ad31c3011880b4133cafd4bd0", "https://bcr.bazel.build/modules/rules_license/0.0.7/MODULE.bazel": "088fbeb0b6a419005b89cf93fe62d9517c0a2b8bb56af3244af65ecfe37e7d5d", "https://bcr.bazel.build/modules/rules_license/1.0.0/MODULE.bazel": "a7fda60eefdf3d8c827262ba499957e4df06f659330bbe6cdbdb975b768bb65c", "https://bcr.bazel.build/modules/rules_license/1.0.0/source.json": "a52c89e54cc311196e478f8382df91c15f7a2bfdf4c6cd0e2675cc2ff0b56efb", "https://bcr.bazel.build/modules/rules_pkg/0.7.0/MODULE.bazel": "df99f03fc7934a4737122518bb87e667e62d780b610910f0447665a7e2be62dc", "https://bcr.bazel.build/modules/rules_pkg/1.0.1/MODULE.bazel": "5b1df97dbc29623bccdf2b0dcd0f5cb08e2f2c9050aab1092fd39a41e82686ff", "https://bcr.bazel.build/modules/rules_pkg/1.0.1/source.json": "bd82e5d7b9ce2d31e380dd9f50c111d678c3bdaca190cb76b0e1c71b05e1ba8a", "https://bcr.bazel.build/modules/rules_proto/4.0.0/MODULE.bazel": "a7a7b6ce9bee418c1a760b3d84f83a299ad6952f9903c67f19e4edd964894e06", "https://bcr.bazel.build/modules/rules_proto/5.3.0-21.7/MODULE.bazel": "e8dff86b0971688790ae75528fe1813f71809b5afd57facb44dad9e8eca631b7", "https://bcr.bazel.build/modules/rules_proto/6.0.0-rc1/MODULE.bazel": "1e5b502e2e1a9e825eef74476a5a1ee524a92297085015a052510b09a1a09483", "https://bcr.bazel.build/modules/rules_proto/6.0.0/MODULE.bazel": "b531d7f09f58dce456cd61b4579ce8c86b38544da75184eadaf0a7cb7966453f", "https://bcr.bazel.build/modules/rules_proto/6.0.2/MODULE.bazel": "ce916b775a62b90b61888052a416ccdda405212b6aaeb39522f7dc53431a5e73", "https://bcr.bazel.build/modules/rules_proto/7.1.0/MODULE.bazel": "002d62d9108f75bb807cd56245d45648f38275cb3a99dcd45dfb864c5d74cb96", "https://bcr.bazel.build/modules/rules_proto/7.1.0/source.json": "39f89066c12c24097854e8f57ab8558929f9c8d474d34b2c00ac04630ad8940e", "https://bcr.bazel.build/modules/rules_python/0.10.2/MODULE.bazel": "cc82bc96f2997baa545ab3ce73f196d040ffb8756fd2d66125a530031cd90e5f", "https://bcr.bazel.build/modules/rules_python/0.22.1/MODULE.bazel": "26114f0c0b5e93018c0c066d6673f1a2c3737c7e90af95eff30cfee38d0bbac7", "https://bcr.bazel.build/modules/rules_python/0.23.1/MODULE.bazel": "49ffccf0511cb8414de28321f5fcf2a31312b47c40cc21577144b7447f2bf300", "https://bcr.bazel.build/modules/rules_python/0.25.0/MODULE.bazel": "72f1506841c920a1afec76975b35312410eea3aa7b63267436bfb1dd91d2d382", "https://bcr.bazel.build/modules/rules_python/0.28.0/MODULE.bazel": "cba2573d870babc976664a912539b320cbaa7114cd3e8f053c720171cde331ed", "https://bcr.bazel.build/modules/rules_python/0.31.0/MODULE.bazel": "93a43dc47ee570e6ec9f5779b2e64c1476a6ce921c48cc9a1678a91dd5f8fd58", "https://bcr.bazel.build/modules/rules_python/0.32.2/MODULE.bazel": "01052470fc30b49de91fb8483d26bea6f664500cfad0b078d4605b03e3a83ed4", "https://bcr.bazel.build/modules/rules_python/0.32.2/source.json": "d0442db378276bcdc3bed9ce6d7018ab4022e99401d89e3c50dfecac874ca74f", "https://bcr.bazel.build/modules/rules_python/0.4.0/MODULE.bazel": "9208ee05fd48bf09ac60ed269791cf17fb343db56c8226a720fbb1cdf467166c", "https://bcr.bazel.build/modules/rules_shell/0.2.0/MODULE.bazel": "fda8a652ab3c7d8fee214de05e7a9916d8b28082234e8d2c0094505c5268ed3c", "https://bcr.bazel.build/modules/rules_shell/0.2.0/source.json": "7f27af3c28037d9701487c4744b5448d26537cc66cdef0d8df7ae85411f8de95", "https://bcr.bazel.build/modules/rules_shellcheck/0.4.0/MODULE.bazel": "e779debc3c2900d9ebe065c65b2972a99630894ea18df7d469f24c0f4e346539", "https://bcr.bazel.build/modules/rules_shellcheck/0.4.0/source.json": "53f90843000414a45be2cc92cfc936b72456df1fd0d11ea79014f1baa3cf3458", "https://bcr.bazel.build/modules/stardoc/0.5.6/MODULE.bazel": "c43dabc564990eeab55e25ed61c07a1aadafe9ece96a4efabb3f8bf9063b71ef", "https://bcr.bazel.build/modules/stardoc/0.7.0/MODULE.bazel": "05e3d6d30c099b6770e97da986c53bd31844d7f13d41412480ea265ac9e8079c", "https://bcr.bazel.build/modules/stardoc/0.7.0/source.json": "e3c524bf2ef20992539ce2bc4a2243f4853130209ee831689983e28d05769099", "https://bcr.bazel.build/modules/upb/0.0.0-20211020-160625a/MODULE.bazel": "6cced416be2dc5b9c05efd5b997049ba795e5e4e6fafbe1624f4587767638928", "https://bcr.bazel.build/modules/upb/0.0.0-20220923-a547704/MODULE.bazel": "7298990c00040a0e2f121f6c32544bab27d4452f80d9ce51349b1a28f3005c43", "https://bcr.bazel.build/modules/upb/0.0.0-20220923-a547704/source.json": "f1ef7d3f9e0e26d4b23d1c39b5f5de71f584dd7d1b4ef83d9bbba6ec7a6a6459", "https://bcr.bazel.build/modules/zlib/1.2.11/MODULE.bazel": "07b389abc85fdbca459b69e2ec656ae5622873af3f845e1c9d80fe179f3effa0", "https://bcr.bazel.build/modules/zlib/1.2.12/MODULE.bazel": "3b1a8834ada2a883674be8cbd36ede1b6ec481477ada359cd2d3ddc562340b27", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.3/MODULE.bazel": "af322bc08976524477c79d1e45e241b6efbeb918c497e8840b8ab116802dda79", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.3/source.json": "2be409ac3c7601245958cd4fcdff4288be79ed23bd690b4b951f500d54ee6e7d", "https://bcr.bazel.build/modules/zlib/1.3.1/MODULE.bazel": "751c9940dcfe869f5f7274e1295422a34623555916eb98c174c1e945594bf198"}, "selectedYankedVersions": {}, "moduleExtensions": {"//server/scripts/build-module/chromedriver:extensions.bzl%mongodb_chromedriver_extension": {"general": {"bzlTransitiveDigest": "rojRhYAmLB5rMo9UsEjzL5Z0n9YlS7MWZ3QEsmWWwz4=", "usagesDigest": "ljWDuB7W+CqaKxQFNhVIIpx74pTNfi+HdC7xIJ/5UnI=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {"chrome_path": null}, "generatedRepoSpecs": {"chromedriver.linux_x86_64": {"bzlFile": "@@//server/scripts/build-module/chromedriver:extensions.bzl", "ruleClassName": "_chromedriver_repo", "attributes": {"chromedriver_platform": "linux64", "url": "https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/linux64/chromedriver-linux64.zip", "strip_prefix": true}}, "chromedriver.macos_arm64": {"bzlFile": "@@//server/scripts/build-module/chromedriver:extensions.bzl", "ruleClassName": "_chromedriver_repo", "attributes": {"chromedriver_platform": "mac-arm64", "url": "https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/mac-arm64/chromedriver-mac-arm64.zip", "strip_prefix": true}}, "chromedriver.macos_x86_64": {"bzlFile": "@@//server/scripts/build-module/chromedriver:extensions.bzl", "ruleClassName": "_chromedriver_repo", "attributes": {"chromedriver_platform": "mac-x64", "url": "https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.157/mac-x64/chromedriver-mac-x64.zip", "strip_prefix": true}}}, "recordedRepoMappingEntries": []}}, "//third_party:third_party_extension.bzl%third_party_extension": {"general": {"bzlTransitiveDigest": "RZQVWeDcObuaaXNqd2c1YsofrfALFQV/7QndxIKwdXg=", "usagesDigest": "YjnCVqbu7PS6Ch78J8Ye0i3bjDZkOXjZlDZZvTSXSwQ=", "recordedFileInputs": {"@@//third_party/conf/migrate.json": "cd76f2476579d4cd98ea7744ab1d8ed557519194028a57421ee992b5c4b65c11", "@@//third_party/conf/drone.json": "aede8a3da757e2a470c9abeec5f9a721ba3f47d51015472269861137022957f0", "@@//third_party/conf/oasdiff.json": "013fd46e817b88f9425b97d9f15791fdeca190af31704d9fa787392610dd8c8d", "@@//third_party/conf/bomber.json": "c3538c2a238cb89dd67ee36a9133149633c718c313310e81cd58b62517b125b8", "@@//third_party/conf/spectral.json": "abddd99ece8721418670c29cd96247eb63f7ee34a8fb6db6374f10e38219e8a2", "@@//third_party/conf": "DIR", "@@//third_party/conf/yq.json": "627b7aec56d84cb9c755733df3ff9f456aaee4137fe493d2d0349e465574832b", "@@//third_party/conf/github_graphql_schema.json": "8f24cb32077584e5980721296639be125019c4637da7f9a36328d47ef551256a", "@@//third_party/conf/k0s.json": "f41d381d09728a8ff8ed92960e030f1ee31ab150058ab8ef86baf32ce8c3007c", "@@//third_party/conf/snyk.json": "c770065b6c03c9811c77b89f73d0b183973cd1ee551447ddc3f2f336030c2c9d", "@@//third_party/conf/gitcli.json": "80ecd4eb56cfae4f6ce06cf5a66ebd1842eae2f5de4cfa1c177ece3fc4b2d9f8", "@@//third_party/conf/jq.json": "69f48038352bf432d3dfa65f5f737a3220f1490d0e7cd6e551684ade8afb4fcb", "@@//third_party/conf/d2.json": "3cf677a3713493d4d1333f70db3449b4bcdf70d4851365c3a623972e4e20204f", "@@//third_party/conf/foascli.json": "59ac759c8a1bf4d5f57c29a2d13b91d2845226eb21e7991b1bfce5eb3d051805"}, "recordedDirentsInputs": {"@@//third_party/conf": "e43d4daf72d0bc600a8c4d6c1d35db7c5ca20abdbea751605a8c0f2d37d25ee8"}, "envVariables": {}, "generatedRepoSpecs": {"d2.linux_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "d2", "url": "https://github.com/terrastruct/d2/releases/download/v0.6.9/d2-v0.6.9-linux-arm64.tar.gz", "sha256": "44d66f2ec64419358c4bad11b0275022dd88c68259f8eaa7208ca9502601b8c6", "executable": false, "build_file": "@@//third_party/conf:d2.BUILD"}}, "d2.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "d2", "url": "https://github.com/terrastruct/d2/releases/download/v0.6.9/d2-v0.6.9-linux-amd64.tar.gz", "sha256": "5b72a7065ba2da0564acca9a44ec3a4205a4a69686990f947568b7ffa5f7f443", "executable": false, "build_file": "@@//third_party/conf:d2.BUILD"}}, "d2.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "d2", "url": "https://github.com/terrastruct/d2/releases/download/v0.6.9/d2-v0.6.9-macos-arm64.tar.gz", "sha256": "efb79d23d98033da8a441dd18296e77da2898482ced30433df84e21c93851857", "executable": false, "build_file": "@@//third_party/conf:d2.BUILD"}}, "d2.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "d2", "url": "https://github.com/terrastruct/d2/releases/download/v0.6.9/d2-v0.6.9-macos-amd64.tar.gz", "sha256": "b6909c409e4412ca67bb28aef3e8daa876de64796438f10730448fe2dbbfbd1f", "executable": false, "build_file": "@@//third_party/conf:d2.BUILD"}}, "yq.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "yq", "url": "https://github.com/mikefarah/yq/releases/download/v4.43.1/yq_linux_amd64", "sha256": "cfbbb9ba72c9402ef4ab9d8f843439693dfb380927921740e51706d90869c7e1", "executable": true, "build_file": "@@//third_party/conf:yq.BUILD"}}, "yq.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "yq", "url": "https://github.com/mikefarah/yq/releases/download/v4.43.1/yq_darwin_arm64", "sha256": "9f1063d910698834cb9176593aa288471898031929138d226c2c2de9f262f8e5", "executable": true, "build_file": "@@//third_party/conf:yq.BUILD"}}, "yq.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "yq", "url": "https://github.com/mikefarah/yq/releases/download/v4.43.1/yq_darwin_amd64", "sha256": "fdc42b132ac460037f4f0f48caea82138772c651d91cfbb735210075ddfdbaed", "executable": true, "build_file": "@@//third_party/conf:yq.BUILD"}}, "gitcli.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "gitcli", "url": "https://github.com/cli/cli/releases/download/v2.58.0/gh_2.58.0_linux_amd64.tar.gz", "sha256": "84feae3d143bc360ea1004b474f124c8cfd75363a5e197d3ce63fe23d9f3a2ea", "executable": false, "build_file": "@@//third_party/conf:gitcli.BUILD"}}, "gitcli.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "gitcli", "url": "https://github.com/cli/cli/releases/download/v2.58.0/gh_2.58.0_macOS_amd64.zip", "sha256": "757f004d55414fef3fe1af25e961ae291468c51d86abef7293ee10c8fb60e76b", "executable": false, "build_file": "@@//third_party/conf:gitcli.BUILD"}}, "gitcli.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "gitcli", "url": "https://github.com/cli/cli/releases/download/v2.58.0/gh_2.58.0_macOS_arm64.zip", "sha256": "1861dea614d23edbf3ca05e0376171ed17667cea26bcac2efd1a2c4952d4f8e9", "executable": false, "build_file": "@@//third_party/conf:gitcli.BUILD"}}, "drone.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "drone", "url": "https://github.com/harness/drone-cli/releases/download/v1.8.0/drone_linux_amd64.tar.gz", "sha256": "ecd9d91c8c3d7208ccb843a44bcbcd304fa05cfd5f297919b6b15e0ebf951474", "executable": false, "build_file": "@@//third_party/conf:drone.BUILD"}}, "drone.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "drone", "url": "https://github.com/harness/drone-cli/releases/download/v1.8.0/drone_darwin_amd64.tar.gz", "sha256": "7c5bdbeb5441637fe5caa4e4129de82f1cc34b42349761a83cea36fdd5ef1e10", "executable": false, "build_file": "@@//third_party/conf:drone.BUILD"}}, "drone.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "drone", "url": "https://github.com/harness/drone-cli/releases/download/v1.8.0/drone_darwin_arm64.tar.gz", "sha256": "17413a00689aa9de4b202873d8943dfe92668ef0fbc61c596f469b3897a59058", "executable": false, "build_file": "@@//third_party/conf:drone.BUILD"}}, "migrate.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "migrate", "url": "https://github.com/AntonOyung/migrate/releases/download/v1.0.1/migrate.linux-amd64.tar.gz", "sha256": "c08eb7ce2272c39528c7d5d7b9958910023f430fc8e3245d62a220b020f57459", "executable": false, "build_file": "@@//third_party/conf:migrate.BUILD"}}, "migrate.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "migrate", "url": "https://github.com/AntonOyung/migrate/releases/download/v1.0.1/migrate.darwin-amd64.tar.gz", "sha256": "fbfe37c51d366c07dda0e33d5506ce2afde5eb1f903a1996a6fc7eeacac4a82d", "executable": false, "build_file": "@@//third_party/conf:migrate.BUILD"}}, "migrate.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "migrate", "url": "https://github.com/AntonOyung/migrate/releases/download/v1.0.1/migrate.darwin-amd64.tar.gz", "sha256": "fbfe37c51d366c07dda0e33d5506ce2afde5eb1f903a1996a6fc7eeacac4a82d", "executable": false, "build_file": "@@//third_party/conf:migrate.BUILD"}}, "github_graphql_schema": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "github_graphql_schema", "url": "https://docs.github.com/public/fpt/schema.docs.graphql", "file": true, "output": "downloaded"}}, "jq.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "jq", "url": "https://github.com/jqlang/jq/releases/download/jq-1.7.1/jq-linux64", "sha256": "5942c9b0934e510ee61eb3e30273f1b3fe2590df93933a93d7c58b81d19c8ff5", "executable": true, "build_file": "@@//third_party/conf:jq.BUILD"}}, "jq.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "jq", "url": "https://github.com/jqlang/jq/releases/download/jq-1.7.1/jq-macos-amd64", "sha256": "4155822bbf5ea90f5c79cf254665975eb4274d426d0709770c21774de5407443", "executable": true, "build_file": "@@//third_party/conf:jq.BUILD"}}, "jq.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "jq", "url": "https://github.com/jqlang/jq/releases/download/jq-1.7.1/jq-macos-arm64", "sha256": "0bbe619e663e0de2c550be2fe0d240d076799d6f8a652b70fa04aea8a8362e8a", "executable": true, "build_file": "@@//third_party/conf:jq.BUILD"}}, "snyk.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "snyk", "url": "https://github.com/snyk/cli/releases/download/v1.1103.0/snyk-linux", "sha256": "0b2a00989f20a06e77446c64cce99d11044f1e325f32ff20606e55a75e136fea", "executable": true, "build_file": "@@//third_party/conf:snyk.BUILD"}}, "snyk.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "snyk", "url": "https://github.com/snyk/cli/releases/download/v1.1103.0/snyk-macos", "sha256": "78bbeb7b2aa3e1168ce895a7d03dfaaca7d92efa30d9b05dd295d4d5b6c583c3", "executable": true, "build_file": "@@//third_party/conf:snyk.BUILD"}}, "snyk.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "snyk", "url": "https://github.com/snyk/cli/releases/download/v1.1103.0/snyk-macos", "sha256": "78bbeb7b2aa3e1168ce895a7d03dfaaca7d92efa30d9b05dd295d4d5b6c583c3", "executable": true, "build_file": "@@//third_party/conf:snyk.BUILD"}}, "k0s.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "k0s", "url": "https://github.com/k0sproject/k0s/releases/download/v1.32.7+k0s.0/k0s-v1.32.7+k0s.0-amd64", "sha256": "dc2383ec89ea023dfa69681116680d9e041fd53c0f038b294d3f2e452d537ff2", "executable": true, "build_file": "@@//third_party/conf:k0s.BUILD"}}, "k0s.linux_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "k0s", "url": "https://github.com/k0sproject/k0s/releases/download/v1.32.7%2Bk0s.0/k0s-v1.32.7+k0s.0-arm64", "sha256": "625d89c00e8236b812a24955894077188e91213e9d1dc9440f0e4e6e6db53ffb", "executable": true, "build_file": "@@//third_party/conf:k0s.BUILD"}}, "foascli.linux_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "foascli", "url": "https://github.com/mongodb/openapi/releases/download/v0.0.55/mongodb-foas-cli_0.0.55_linux_arm64.tar.gz", "sha256": "65ed016aeac38fadfaba4ba3200f1fcb77fd6c186f27c5c9a338b2d0d731f6a3", "executable": false, "build_file": "@@//third_party/conf:foascli.BUILD"}}, "foascli.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "foascli", "url": "https://github.com/mongodb/openapi/releases/download/v0.0.55/mongodb-foas-cli_0.0.55_linux_x86_64.tar.gz", "sha256": "facd30799f1bb46ac8dd90d452032d685d404d8e34d92b23d94d925d1cc21f3c", "executable": false, "build_file": "@@//third_party/conf:foascli.BUILD"}}, "foascli.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "foascli", "url": "https://github.com/mongodb/openapi/releases/download/v0.0.55/mongodb-foas-cli_0.0.55_macos_arm64.zip", "sha256": "68dad7548398f5bb64a906475548f2eb348e861ea2acc6ec6c2149da00559bcd", "executable": false, "build_file": "@@//third_party/conf:foascli.BUILD"}}, "foascli.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "foascli", "url": "https://github.com/mongodb/openapi/releases/download/v0.0.55/mongodb-foas-cli_0.0.55_macos_x86_64.zip", "sha256": "15ef558b72a8594f4a026935a6db394a3c22b65eb821bf75818a1a21b3fb6671", "executable": false, "build_file": "@@//third_party/conf:foascli.BUILD"}}, "oasdiff.linux_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "oasdiff", "url": "https://github.com/Tufin/oasdiff/releases/download/v1.10.23/oasdiff_1.10.23_linux_arm64.tar.gz", "sha256": "aa6bcf52f92ffca759a35cc1c818a627cfcc3b7b42e68677520984ab3cf492f8", "executable": false, "build_file": "@@//third_party/conf:oasdiff.BUILD"}}, "oasdiff.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "oasdiff", "url": "https://github.com/Tufin/oasdiff/releases/download/v1.10.23/oasdiff_1.10.23_linux_amd64.tar.gz", "sha256": "7d947cfebc2fcd9ffa2754e3634620debace4822dc9ff9fd046ea7cabb6604a4", "executable": false, "build_file": "@@//third_party/conf:oasdiff.BUILD"}}, "oasdiff.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "oasdiff", "url": "https://github.com/Tufin/oasdiff/releases/download/v1.10.23/oasdiff_1.10.23_darwin_all.tar.gz", "sha256": "6431dcb019aba45d196abeaf650a4282ff977e21089e5a858722d2cf97452936", "executable": false, "build_file": "@@//third_party/conf:oasdiff.BUILD"}}, "oasdiff.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "oasdiff", "url": "https://github.com/Tufin/oasdiff/releases/download/v1.10.23/oasdiff_1.10.23_darwin_all.tar.gz", "sha256": "6431dcb019aba45d196abeaf650a4282ff977e21089e5a858722d2cf97452936", "executable": false, "build_file": "@@//third_party/conf:oasdiff.BUILD"}}, "bomber.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "bomber", "url": "https://github.com/devops-kung-fu/bomber/releases/download/v0.4.8/bomber_0.4.8_linux_amd64.tar.gz", "sha256": "da9f41d9df113a34f2c6c76d7b1cad39af73c5ba10499c3be24486655fa4fe0a", "executable": false, "build_file": "@@//third_party/conf:bomber.BUILD"}}, "bomber.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "bomber", "url": "https://github.com/devops-kung-fu/bomber/releases/download/v0.4.8/bomber_0.4.8_darwin_all.tar.gz", "sha256": "2ffc0c76d595f67d53ac384a1a7e0aea161727aed0ebb641f0636aedacd50d31", "executable": false, "build_file": "@@//third_party/conf:bomber.BUILD"}}, "bomber.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "bomber", "url": "https://github.com/devops-kung-fu/bomber/releases/download/v0.4.8/bomber_0.4.8_darwin_all.tar.gz", "sha256": "2ffc0c76d595f67d53ac384a1a7e0aea161727aed0ebb641f0636aedacd50d31", "executable": false, "build_file": "@@//third_party/conf:bomber.BUILD"}}, "spectral.linux_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "spectral", "url": "https://github.com/stoplightio/spectral/releases/download/v6.14.2/spectral-linux-x64", "sha256": "098c96fcb8c3e17e46011ea671a5d7c5df97c3a80ca32d701ccc7e3de0a1c42d", "executable": true, "build_file": "@@//third_party/conf:spectral.BUILD"}}, "spectral.macos_x86_64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "spectral", "url": "https://github.com/stoplightio/spectral/releases/download/v6.14.2/spectral-macos-x64", "sha256": "90954e5b16eb3446d05ae307a5ab92be6a75c2ae6a8cf56e04149a3139609122", "executable": true, "build_file": "@@//third_party/conf:spectral.BUILD"}}, "spectral.macos_arm64": {"bzlFile": "@@//third_party:third_party_extension.bzl", "ruleClassName": "_download_third_party_repo", "attributes": {"third_party_name": "spectral", "url": "https://github.com/stoplightio/spectral/releases/download/v6.14.2/spectral-macos-arm64", "sha256": "dec0471edf1d981a3bbd96241ed4e8bf12ec4565f478ed1eaae7ee7785d97fad", "executable": true, "build_file": "@@//third_party/conf:spectral.BUILD"}}}, "recordedRepoMappingEntries": []}}, "//toolchains/java:extensions.bzl%mongodb_java_extension": {"general": {"bzlTransitiveDigest": "DPi55mz5QmpXc/oqoSKC2CTXt9NIxjTeLzFToPVm6OY=", "usagesDigest": "ppJ91UhMVFaGtmk3hSc+AiFRyQLrV4nJSbiA1E54f6w=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"mongodb_jdk17": {"bzlFile": "@@//toolchains/java:extensions.bzl", "ruleClassName": "_jdk_repo", "attributes": {"version": "17"}}, "mongodb_jdk21": {"bzlFile": "@@//toolchains/java:extensions.bzl", "ruleClassName": "_jdk_repo", "attributes": {"version": "21"}}}, "recordedRepoMappingEntries": []}}, "//toolchains/proto:java_protoc_toolchain.bzl%protoc_toolchain_extension": {"general": {"bzlTransitiveDigest": "K7LPuXQ0Tv+DgcnH0pAkzLlbQiBkAL9XGk1lhqRct8w=", "usagesDigest": "iCRrjo6y7cTphdJfT8QDvJmRPs0gSzamKO65F8EHVwI=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"protoc_java_toolchain": {"bzlFile": "@@//toolchains/proto:java_protoc_toolchain.bzl", "ruleClassName": "proto_repo", "attributes": {}}}, "recordedRepoMappingEntries": []}}, "@@apple_support~//crosstool:setup.bzl%apple_cc_configure_extension": {"general": {"bzlTransitiveDigest": "PjIds3feoYE8SGbbIq2SFTZy3zmxeO2tQevJZNDo7iY=", "usagesDigest": "+hz7IHWN6A1oVJJWNDB6yZRG+RYhF76wAYItpAeIUIg=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"local_config_apple_cc_toolchains": {"bzlFile": "@@apple_support~//crosstool:setup.bzl", "ruleClassName": "_apple_cc_autoconf_toolchains", "attributes": {}}, "local_config_apple_cc": {"bzlFile": "@@apple_support~//crosstool:setup.bzl", "ruleClassName": "_apple_cc_autoconf", "attributes": {}}}, "recordedRepoMappingEntries": [["apple_support~", "bazel_tools", "bazel_tools"]]}}, "@@bazel_jar_jar~//internal:non_module_deps.bzl%non_module_deps": {"general": {"bzlTransitiveDigest": "2s/hQiDtUkEr6+trg3Ph5H90TQr2Q4aizRVy4bXVtvA=", "usagesDigest": "8wNM47NKIWiz3KyPnwAfhWFk02ekrs6e4eAU8ydsQb4=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"jvm__jarjar_abrams_assembly": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_jar", "attributes": {"urls": ["https://repo1.maven.org/maven2/com/eed3si9n/jarjarabrams/jarjar-abrams-assembly_2.12/1.14.0/jarjar-abrams-assembly_2.12-1.14.0.jar"], "sha256": "75f86f7588136d6ca92d6fed8d58e6666e04c507b71de378527c053fd2a151c2"}}, "jvm__com_twitter__scalding_args": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_jar", "attributes": {"urls": ["https://repo1.maven.org/maven2/com/twitter/scalding-args_2.12/0.17.4/scalding-args_2.12-0.17.4.jar"], "sha256": "e0de2ad8ef344bb11a2854275b5b85a1adb17f0e0ed9740177d940a602cd977b"}}}, "recordedRepoMappingEntries": [["bazel_jar_jar~", "bazel_tools", "bazel_tools"], ["bazel_tools", "rules_java", "rules_java~"]]}}, "@@openapi_tools_generator_bazel~//:extension.bzl%openapi_gen": {"general": {"bzlTransitiveDigest": "47Cw3TjNUR3ewo7/OKoqGfMf0jc0ca1N2x4LmHjtlto=", "usagesDigest": "bpEVlRq91X62Hvc3HGOV96N4X1thQaWLmGHqyfxaSrM=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"openapi_tools_generator_bazel_cli": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:jvm.bzl", "ruleClassName": "jvm_import_external", "attributes": {"generated_rule_name": "openapi_tools_generator_bazel_cli", "artifact_urls": ["https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.7.0/openapi-generator-cli-7.7.0.jar"], "srcjar_urls": [], "canonical_id": "org.openapitools:openapi-generator-cli:7.7.0", "rule_name": "java_import", "tags": ["maven_coordinates=org.openapitools:openapi-generator-cli:7.7.0"], "artifact_sha256": "3a757276c31d249a4f06a14651b1ff1f1a5cf46e110a70adcc4a6a2834f85561"}}}, "recordedRepoMappingEntries": [["openapi_tools_generator_bazel~", "bazel_tools", "bazel_tools"]]}}, "@@rules_helm~//helm:extensions.bzl%helm": {"general": {"bzlTransitiveDigest": "YQsFlRijaSI9fs+JmGxFgs7v09CLQHNB5pwOJov6shU=", "usagesDigest": "hAGRV5REtBeMzCa0Hv9DITvjdC8xzVXSxJ7fSDgcWHw=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"helm_darwin_amd64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-darwin-amd64.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-IO+N9GcTSab8VWpiG+EXDdcJxsDPX36Dotn7BRX9l/w=", "strip_prefix": "darwin-amd64"}}, "helm_darwin_amd64_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "darwin-amd64", "plugins": [], "exec_compatible_with": ["@platforms//os:macos", "@platforms//cpu:x86_64"]}}, "helm_darwin_arm64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-darwin-arm64.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-ia7EPOB7BiOfG7pKZQcja7SK5Ie8UGWo4lTTzlihaZc=", "strip_prefix": "darwin-arm64"}}, "helm_darwin_arm64_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "darwin-arm64", "plugins": [], "exec_compatible_with": ["@platforms//os:macos", "@platforms//cpu:aarch64"]}}, "helm_linux_amd64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-linux-amd64.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-7oizyFGuZGaj3lB/e+c/6U1Uy/KYfLqj0aODLqMx8s0=", "strip_prefix": "linux-amd64"}}, "helm_linux_amd64_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "linux-amd64", "plugins": [], "exec_compatible_with": ["@platforms//os:linux", "@platforms//cpu:x86_64"]}}, "helm_linux_arm": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-linux-arm.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-YNdtHhLT4Fip6aggnv90im+rWUgCih8IYPSOFBJD0z0=", "strip_prefix": "linux-arm"}}, "helm_linux_arm_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "linux-arm", "plugins": [], "exec_compatible_with": ["@platforms//os:linux", "@platforms//cpu:arm"]}}, "helm_linux_arm64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-linux-arm64.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-eUTj3v04bHb9ktnm/sXC1loyP2+twZv7XnBOPu4QNI4=", "strip_prefix": "linux-arm64"}}, "helm_linux_arm64_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "linux-arm64", "plugins": [], "exec_compatible_with": ["@platforms//os:linux", "@platforms//cpu:aarch64"]}}, "helm_linux_i386": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-linux-386.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-UXQteMBmQ34js8qYNw3zQfkTa0CDgf5aFQ1wudm/JNc=", "strip_prefix": "linux-386"}}, "helm_linux_i386_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "linux-i386", "plugins": [], "exec_compatible_with": ["@platforms//os:linux", "@platforms//cpu:i386"]}}, "helm_linux_ppc64le": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-linux-ppc64le.tar.gz"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-uCGIWlArL6FZ4+86/pzebmyYdtSmI/GIaIKcPuSjxkw=", "strip_prefix": "linux-ppc64le"}}, "helm_linux_ppc64le_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "linux-ppc64le", "plugins": [], "exec_compatible_with": ["@platforms//os:linux", "@platforms//cpu:ppc"]}}, "helm_windows_amd64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"urls": ["https://get.helm.sh/helm-v3.17.3-windows-amd64.zip"], "build_file_content": "package(default_visibility = [\"//visibility:public\"])\nexports_files(glob([\"**\"]))\n", "integrity": "sha256-jqk+L2KF5kne3lg6yQ/4zbk4ylPsbPX+kJ8jA/vCLZY=", "strip_prefix": "windows-amd64"}}, "helm_windows_amd64_toolchain": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_toolchain_repository", "attributes": {"platform": "windows-amd64", "plugins": [], "exec_compatible_with": ["@platforms//os:windows"]}}, "helm": {"bzlFile": "@@rules_helm~//helm:repositories.bzl", "ruleClassName": "helm_host_alias_repository", "attributes": {}}}, "recordedRepoMappingEntries": [["rules_helm~", "bazel_tools", "bazel_tools"]]}}, "@@rules_jvm_external~//:extensions.bzl%maven": {"general": {"bzlTransitiveDigest": "QRQRcniQyBwTIBf9VItGN+egiSvdvBdkft4utJecZf8=", "usagesDigest": "OLEo/rjOSLMJlnvfl5Vnx3aLOJTolHEIBOCGH6qrQIw=", "recordedFileInputs": {"@@stardoc~//maven_install.json": "de0bfa778b4ed6aebb77509362dd87ab8d20fc7c7c18d2a7429cdfee03949a21", "@@rules_jvm_external~//rules_jvm_external_deps_install.json": "cafb5d2d8119391eb2b322ce3840d3352ea82d496bdb8cbd4b6779ec4d044dda"}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"unpinned_rules_jvm_external_deps": {"bzlFile": "@@rules_jvm_external~//:coursier.bzl", "ruleClassName": "coursier_fetch", "attributes": {"user_provided_name": "rules_jvm_external_deps", "repositories": ["{ \"repo_url\": \"https://repo1.maven.org/maven2\" }"], "artifacts": ["{ \"group\": \"com.google.auth\", \"artifact\": \"google-auth-library-credentials\", \"version\": \"1.19.0\" }", "{ \"group\": \"com.google.auth\", \"artifact\": \"google-auth-library-oauth2-http\", \"version\": \"1.19.0\" }", "{ \"group\": \"com.google.cloud\", \"artifact\": \"google-cloud-core\", \"version\": \"2.22.0\" }", "{ \"group\": \"com.google.cloud\", \"artifact\": \"google-cloud-storage\", \"version\": \"2.26.1\" }", "{ \"group\": \"com.google.code.gson\", \"artifact\": \"gson\", \"version\": \"2.10.1\" }", "{ \"group\": \"com.google.googlejavaformat\", \"artifact\": \"google-java-format\", \"version\": \"1.17.0\" }", "{ \"group\": \"com.google.guava\", \"artifact\": \"guava\", \"version\": \"32.1.2-jre\" }", "{ \"group\": \"org.apache.maven\", \"artifact\": \"maven-artifact\", \"version\": \"3.9.4\" }", "{ \"group\": \"software.amazon.awssdk\", \"artifact\": \"s3\", \"version\": \"2.20.128\" }"], "fail_on_missing_checksum": true, "fetch_sources": false, "fetch_javadoc": false, "excluded_artifacts": [], "generate_compat_repositories": false, "version_conflict_policy": "default", "override_targets": {}, "strict_visibility": false, "strict_visibility_value": ["@@//visibility:private"], "maven_install_json": "@@rules_jvm_external~//:rules_jvm_external_deps_install.json", "resolve_timeout": 600, "use_starlark_android_rules": false, "aar_import_bzl_label": "@build_bazel_rules_android//android:rules.bzl", "duplicate_version_warning": "warn", "ignore_empty_files": false}}, "com_fasterxml_jackson_core_jackson_core_2_15_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "303c99e82b1faa91a0bae5d8fbeb56f7e2adf9b526a900dd723bf140d62bd4b4", "urls": ["https://repo1.maven.org/maven2/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"], "downloaded_file_path": "v1/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar"}}, "com_google_android_annotations_4_1_1_4": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "ba734e1e84c09d615af6a09d33034b4f0442f8772dec120efb376d86a565ae15", "urls": ["https://repo1.maven.org/maven2/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar"], "downloaded_file_path": "v1/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar"}}, "com_google_api_client_google_api_client_2_2_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "58eca9fb0a869391689ffc828b3bd0b19ac76042ff9fab4881eddf7fde76903f", "urls": ["https://repo1.maven.org/maven2/com/google/api-client/google-api-client/2.2.0/google-api-client-2.2.0.jar"], "downloaded_file_path": "v1/com/google/api-client/google-api-client/2.2.0/google-api-client-2.2.0.jar"}}, "com_google_api_grpc_gapic_google_cloud_storage_v2_2_26_1_alpha": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "4b1b414751ed08dfc9f5e7e93c3fa16b8c53de5d24bf2ded414240fa72842e09", "urls": ["https://repo1.maven.org/maven2/com/google/api/grpc/gapic-google-cloud-storage-v2/2.26.1-alpha/gapic-google-cloud-storage-v2-2.26.1-alpha.jar"], "downloaded_file_path": "v1/com/google/api/grpc/gapic-google-cloud-storage-v2/2.26.1-alpha/gapic-google-cloud-storage-v2-2.26.1-alpha.jar"}}, "com_google_api_grpc_grpc_google_cloud_storage_v2_2_26_1_alpha": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "c5fa3121300bf3558248792ca8279f13208b395f6ba5e004ae32fcb2964810bd", "urls": ["https://repo1.maven.org/maven2/com/google/api/grpc/grpc-google-cloud-storage-v2/2.26.1-alpha/grpc-google-cloud-storage-v2-2.26.1-alpha.jar"], "downloaded_file_path": "v1/com/google/api/grpc/grpc-google-cloud-storage-v2/2.26.1-alpha/grpc-google-cloud-storage-v2-2.26.1-alpha.jar"}}, "com_google_api_grpc_proto_google_cloud_storage_v2_2_26_1_alpha": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "e1c33f066db9189f09d1b7ec698f939eb4591f937fcd1ca1cbd4f05f1eb0e25c", "urls": ["https://repo1.maven.org/maven2/com/google/api/grpc/proto-google-cloud-storage-v2/2.26.1-alpha/proto-google-cloud-storage-v2-2.26.1-alpha.jar"], "downloaded_file_path": "v1/com/google/api/grpc/proto-google-cloud-storage-v2/2.26.1-alpha/proto-google-cloud-storage-v2-2.26.1-alpha.jar"}}, "com_google_api_grpc_proto_google_common_protos_2_23_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "ff880ec7fae731bed60377871fa3138ad6ea6fd31d0c6055c2e70ea47917402b", "urls": ["https://repo1.maven.org/maven2/com/google/api/grpc/proto-google-common-protos/2.23.0/proto-google-common-protos-2.23.0.jar"], "downloaded_file_path": "v1/com/google/api/grpc/proto-google-common-protos/2.23.0/proto-google-common-protos-2.23.0.jar"}}, "com_google_api_grpc_proto_google_iam_v1_1_18_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "11ba274f3b23fae7985a51336ab45fcf24bf655604bdbfedc6d9701288fcc4cd", "urls": ["https://repo1.maven.org/maven2/com/google/api/grpc/proto-google-iam-v1/1.18.0/proto-google-iam-v1-1.18.0.jar"], "downloaded_file_path": "v1/com/google/api/grpc/proto-google-iam-v1/1.18.0/proto-google-iam-v1-1.18.0.jar"}}, "com_google_api_api_common_2_15_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "8c56f69021f1e6dc5bbf5597459220df176d78278456c5a80b47369c83af251b", "urls": ["https://repo1.maven.org/maven2/com/google/api/api-common/2.15.0/api-common-2.15.0.jar"], "downloaded_file_path": "v1/com/google/api/api-common/2.15.0/api-common-2.15.0.jar"}}, "com_google_api_gax_2_32_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "eedeceb93a8d92e3b5d9781c87db1deb3d72eb545ae4e27a18cddde4100a5173", "urls": ["https://repo1.maven.org/maven2/com/google/api/gax/2.32.0/gax-2.32.0.jar"], "downloaded_file_path": "v1/com/google/api/gax/2.32.0/gax-2.32.0.jar"}}, "com_google_api_gax_grpc_2_32_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "79e4c7910c74b3ca0e709665f36e061538f80d98b53e5168c301508d0159758d", "urls": ["https://repo1.maven.org/maven2/com/google/api/gax-grpc/2.32.0/gax-grpc-2.32.0.jar"], "downloaded_file_path": "v1/com/google/api/gax-grpc/2.32.0/gax-grpc-2.32.0.jar"}}, "com_google_api_gax_httpjson_2_32_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "5830038e076277d105cde00054c63926b98493d684634eb3c7f4318328d80ca0", "urls": ["https://repo1.maven.org/maven2/com/google/api/gax-httpjson/2.32.0/gax-httpjson-2.32.0.jar"], "downloaded_file_path": "v1/com/google/api/gax-httpjson/2.32.0/gax-httpjson-2.32.0.jar"}}, "com_google_apis_google_api_services_storage_v1_rev20230617_2_0_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "43484b32b410b2b8ff32ac9ab1b89c039c727c2e37465e375ce2846d5a804645", "urls": ["https://repo1.maven.org/maven2/com/google/apis/google-api-services-storage/v1-rev20230617-2.0.0/google-api-services-storage-v1-rev20230617-2.0.0.jar"], "downloaded_file_path": "v1/com/google/apis/google-api-services-storage/v1-rev20230617-2.0.0/google-api-services-storage-v1-rev20230617-2.0.0.jar"}}, "com_google_auth_google_auth_library_credentials_1_19_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "095984b0594888a47f311b3c9dcf6da9ed86feeea8f78140c55e14c27b0593e5", "urls": ["https://repo1.maven.org/maven2/com/google/auth/google-auth-library-credentials/1.19.0/google-auth-library-credentials-1.19.0.jar"], "downloaded_file_path": "v1/com/google/auth/google-auth-library-credentials/1.19.0/google-auth-library-credentials-1.19.0.jar"}}, "com_google_auth_google_auth_library_oauth2_http_1_19_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "01bdf5c5cd85e10b794e401775d9909b56a38ffce313fbd39510a5d87ed56f58", "urls": ["https://repo1.maven.org/maven2/com/google/auth/google-auth-library-oauth2-http/1.19.0/google-auth-library-oauth2-http-1.19.0.jar"], "downloaded_file_path": "v1/com/google/auth/google-auth-library-oauth2-http/1.19.0/google-auth-library-oauth2-http-1.19.0.jar"}}, "com_google_auto_value_auto_value_annotations_1_10_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "3f3b7edfaf7fbbd88642f7bd5b09487b8dcf2b9e5f3a19f1eb7b3e53f20f14ba", "urls": ["https://repo1.maven.org/maven2/com/google/auto/value/auto-value-annotations/1.10.2/auto-value-annotations-1.10.2.jar"], "downloaded_file_path": "v1/com/google/auto/value/auto-value-annotations/1.10.2/auto-value-annotations-1.10.2.jar"}}, "com_google_cloud_google_cloud_core_2_22_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "5bc01f00878cb5bf2dcd596cc577979357460f311807aee65aaa6837bdf0eef9", "urls": ["https://repo1.maven.org/maven2/com/google/cloud/google-cloud-core/2.22.0/google-cloud-core-2.22.0.jar"], "downloaded_file_path": "v1/com/google/cloud/google-cloud-core/2.22.0/google-cloud-core-2.22.0.jar"}}, "com_google_cloud_google_cloud_core_grpc_2_22_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "18eeb382b6cf83bfebd49a1c785a2474bb5937aeed15326c4e6d5595416dadf3", "urls": ["https://repo1.maven.org/maven2/com/google/cloud/google-cloud-core-grpc/2.22.0/google-cloud-core-grpc-2.22.0.jar"], "downloaded_file_path": "v1/com/google/cloud/google-cloud-core-grpc/2.22.0/google-cloud-core-grpc-2.22.0.jar"}}, "com_google_cloud_google_cloud_core_http_2_22_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "eba963e2d7aee9cb7dd71872f634d4418c7dffc260f740431b9f577b09417c03", "urls": ["https://repo1.maven.org/maven2/com/google/cloud/google-cloud-core-http/2.22.0/google-cloud-core-http-2.22.0.jar"], "downloaded_file_path": "v1/com/google/cloud/google-cloud-core-http/2.22.0/google-cloud-core-http-2.22.0.jar"}}, "com_google_cloud_google_cloud_storage_2_26_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "6a607268c51471280dc07176b46577951e0e198780a53c6a864fcb2a7acc9902", "urls": ["https://repo1.maven.org/maven2/com/google/cloud/google-cloud-storage/2.26.1/google-cloud-storage-2.26.1.jar"], "downloaded_file_path": "v1/com/google/cloud/google-cloud-storage/2.26.1/google-cloud-storage-2.26.1.jar"}}, "com_google_code_findbugs_jsr305_3_0_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "766ad2a0783f2687962c8ad74ceecc38a28b9f72a2d085ee438b7813e928d0c7", "urls": ["https://repo1.maven.org/maven2/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"], "downloaded_file_path": "v1/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar"}}, "com_google_code_gson_gson_2_10_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "4241c14a7727c34feea6507ec801318a3d4a90f070e4525681079fb94ee4c593", "urls": ["https://repo1.maven.org/maven2/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"], "downloaded_file_path": "v1/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar"}}, "com_google_errorprone_error_prone_annotations_2_18_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "9e6814cb71816988a4fd1b07a993a8f21bb7058d522c162b1de849e19bea54ae", "urls": ["https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar"], "downloaded_file_path": "v1/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar"}}, "com_google_googlejavaformat_google_java_format_1_17_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "631ba54c39f6c20df027dc1420736df2e5e43c581880efdd1e46ddb4ce050e3e", "urls": ["https://repo1.maven.org/maven2/com/google/googlejavaformat/google-java-format/1.17.0/google-java-format-1.17.0.jar"], "downloaded_file_path": "v1/com/google/googlejavaformat/google-java-format/1.17.0/google-java-format-1.17.0.jar"}}, "com_google_guava_failureaccess_1_0_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "a171ee4c734dd2da837e4b16be9df4661afab72a41adaf31eb84dfdaf936ca26", "urls": ["https://repo1.maven.org/maven2/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar"], "downloaded_file_path": "v1/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar"}}, "com_google_guava_guava_32_1_2_jre": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "bc65dea7cfd9e4dacf8419d8af0e741655857d27885bb35d943d7187fc3a8fce", "urls": ["https://repo1.maven.org/maven2/com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar"], "downloaded_file_path": "v1/com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar"}}, "com_google_guava_listenablefuture_9999_0_empty_to_avoid_conflict_with_guava": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b372a037d4230aa57fbeffdef30fd6123f9c0c2db85d0aced00c91b974f33f99", "urls": ["https://repo1.maven.org/maven2/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"], "downloaded_file_path": "v1/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"}}, "com_google_http_client_google_http_client_1_43_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "60aca7428c5a1ff3655b70541a98ff3d70dded48ac1324dae1af39f1b61914af", "urls": ["https://repo1.maven.org/maven2/com/google/http-client/google-http-client/1.43.3/google-http-client-1.43.3.jar"], "downloaded_file_path": "v1/com/google/http-client/google-http-client/1.43.3/google-http-client-1.43.3.jar"}}, "com_google_http_client_google_http_client_apache_v2_1_43_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "4cc8485bdda05607c7d8b95b130168ac82ad80bb3618c608fbf941047a96ac3b", "urls": ["https://repo1.maven.org/maven2/com/google/http-client/google-http-client-apache-v2/1.43.3/google-http-client-apache-v2-1.43.3.jar"], "downloaded_file_path": "v1/com/google/http-client/google-http-client-apache-v2/1.43.3/google-http-client-apache-v2-1.43.3.jar"}}, "com_google_http_client_google_http_client_appengine_1_43_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "66ade3c0e73566ed231032a2bda9f2f8e50e74911f6720bf0ee5233f6e5e033e", "urls": ["https://repo1.maven.org/maven2/com/google/http-client/google-http-client-appengine/1.43.3/google-http-client-appengine-1.43.3.jar"], "downloaded_file_path": "v1/com/google/http-client/google-http-client-appengine/1.43.3/google-http-client-appengine-1.43.3.jar"}}, "com_google_http_client_google_http_client_gson_1_43_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "e31a4edcb9c83954a2587e14fa2f3f8f4aad56152381b3321a3bd0bcae03fa26", "urls": ["https://repo1.maven.org/maven2/com/google/http-client/google-http-client-gson/1.43.3/google-http-client-gson-1.43.3.jar"], "downloaded_file_path": "v1/com/google/http-client/google-http-client-gson/1.43.3/google-http-client-gson-1.43.3.jar"}}, "com_google_http_client_google_http_client_jackson2_1_43_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "8157f93ce7b51a013ea8c514413db6647056e39d7acb829bfc5da5b3bd25db3e", "urls": ["https://repo1.maven.org/maven2/com/google/http-client/google-http-client-jackson2/1.43.3/google-http-client-jackson2-1.43.3.jar"], "downloaded_file_path": "v1/com/google/http-client/google-http-client-jackson2/1.43.3/google-http-client-jackson2-1.43.3.jar"}}, "com_google_j2objc_j2objc_annotations_2_8": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "f02a95fa1a5e95edb3ed859fd0fb7df709d121a35290eff8b74dce2ab7f4d6ed", "urls": ["https://repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar"], "downloaded_file_path": "v1/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar"}}, "com_google_oauth_client_google_oauth_client_1_34_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "193edf97aefa28b93c5892bdc598bac34fa4c396588030084f290b1440e8b98a", "urls": ["https://repo1.maven.org/maven2/com/google/oauth-client/google-oauth-client/1.34.1/google-oauth-client-1.34.1.jar"], "downloaded_file_path": "v1/com/google/oauth-client/google-oauth-client/1.34.1/google-oauth-client-1.34.1.jar"}}, "com_google_protobuf_protobuf_java_3_23_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "18a057f5e0f828daa92b71c19df91f6bcc2aad067ca2cdd6b5698055ca7bcece", "urls": ["https://repo1.maven.org/maven2/com/google/protobuf/protobuf-java/3.23.2/protobuf-java-3.23.2.jar"], "downloaded_file_path": "v1/com/google/protobuf/protobuf-java/3.23.2/protobuf-java-3.23.2.jar"}}, "com_google_protobuf_protobuf_java_util_3_23_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "644975b780d7e8de542dda16d4ceb157b40a52a8be5645221e9fd026ef204b13", "urls": ["https://repo1.maven.org/maven2/com/google/protobuf/protobuf-java-util/3.23.2/protobuf-java-util-3.23.2.jar"], "downloaded_file_path": "v1/com/google/protobuf/protobuf-java-util/3.23.2/protobuf-java-util-3.23.2.jar"}}, "com_google_re2j_re2j_1_7": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "4f657af51ab8bb0909bcc3eb40862d26125af8cbcf92aaaba595fed77f947bc0", "urls": ["https://repo1.maven.org/maven2/com/google/re2j/re2j/1.7/re2j-1.7.jar"], "downloaded_file_path": "v1/com/google/re2j/re2j/1.7/re2j-1.7.jar"}}, "commons_codec_commons_codec_1_15": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b3e9f6d63a790109bf0d056611fbed1cf69055826defeb9894a71369d246ed63", "urls": ["https://repo1.maven.org/maven2/commons-codec/commons-codec/1.15/commons-codec-1.15.jar"], "downloaded_file_path": "v1/commons-codec/commons-codec/1.15/commons-codec-1.15.jar"}}, "commons_logging_commons_logging_1_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "daddea1ea0be0f56978ab3006b8ac92834afeefbd9b7e4e6316fca57df0fa636", "urls": ["https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"], "downloaded_file_path": "v1/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"}}, "io_grpc_grpc_alts_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "04317f8835b3a8736ba12a7a25e474430c7f2d8c0b7afc433c2abc4cb2f0d4e8", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-alts/1.56.1/grpc-alts-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-alts/1.56.1/grpc-alts-1.56.1.jar"}}, "io_grpc_grpc_api_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b090b1bb5a3b066f7f2ef14b9ba68e3304de80ba34f90414aed3b519c30999e8", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-api/1.56.1/grpc-api-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-api/1.56.1/grpc-api-1.56.1.jar"}}, "io_grpc_grpc_auth_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "ac365e11532a4b779a2ac80ecc64dcbd3bafbdd666e08e22ffdb5c855069e3f9", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-auth/1.56.1/grpc-auth-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-auth/1.56.1/grpc-auth-1.56.1.jar"}}, "io_grpc_grpc_context_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "3d442ce08bfb1b487edf76d12e2dfd991c3877af32cf772a83c73d06f89743bc", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-context/1.56.1/grpc-context-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-context/1.56.1/grpc-context-1.56.1.jar"}}, "io_grpc_grpc_core_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "fddeafc25019b7e5600028d6398e9ed7383056d9aecaf95aec5c39c5085a4830", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-core/1.56.1/grpc-core-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-core/1.56.1/grpc-core-1.56.1.jar"}}, "io_grpc_grpc_googleapis_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "39b880dc2da28695984bdb77c1fb052e2d3e446d1fbd902e00ea27bebf5f7860", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-googleapis/1.56.1/grpc-googleapis-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-googleapis/1.56.1/grpc-googleapis-1.56.1.jar"}}, "io_grpc_grpc_grpclb_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "6ba786cc5271c7355cb0cdb57660d807cbf0f082b50edae15232e8c354228496", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-grpclb/1.56.1/grpc-grpclb-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-grpclb/1.56.1/grpc-grpclb-1.56.1.jar"}}, "io_grpc_grpc_netty_shaded_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b15257e1137d609a7e8eb9bf4f0cec06b78ee69c030282db0a66d17cc9c3eaf1", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-netty-shaded/1.56.1/grpc-netty-shaded-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-netty-shaded/1.56.1/grpc-netty-shaded-1.56.1.jar"}}, "io_grpc_grpc_protobuf_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "46185731a718d723d853723610a77e9062da9a6fc8b4ff14f370ba10cf097893", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-protobuf/1.56.1/grpc-protobuf-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-protobuf/1.56.1/grpc-protobuf-1.56.1.jar"}}, "io_grpc_grpc_protobuf_lite_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "5605030f1668edf93ade7f24b0bfe5ecf943774e02cf0ac5cac02387ac910185", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-protobuf-lite/1.56.1/grpc-protobuf-lite-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-protobuf-lite/1.56.1/grpc-protobuf-lite-1.56.1.jar"}}, "io_grpc_grpc_rls_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "ff56fa9750087f9deea2d00e08f46c7a3fd40f1032c3f5b44a702c595ddb7f55", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-rls/1.56.1/grpc-rls-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-rls/1.56.1/grpc-rls-1.56.1.jar"}}, "io_grpc_grpc_services_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "0d14ece28e97b30aa9ef1b63782d48261dd63738ef1c5615afefb8b963c121c8", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-services/1.56.1/grpc-services-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-services/1.56.1/grpc-services-1.56.1.jar"}}, "io_grpc_grpc_stub_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "64ffca5dde4565c4c0f876deea3d105341d45ce605b29053e79dc86a22f7953b", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-stub/1.56.1/grpc-stub-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-stub/1.56.1/grpc-stub-1.56.1.jar"}}, "io_grpc_grpc_xds_1_56_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "688950e2dc79c2b227fcad553f4e4c8faf8de324eeccb3a591ff679929bbfa24", "urls": ["https://repo1.maven.org/maven2/io/grpc/grpc-xds/1.56.1/grpc-xds-1.56.1.jar"], "downloaded_file_path": "v1/io/grpc/grpc-xds/1.56.1/grpc-xds-1.56.1.jar"}}, "io_netty_netty_buffer_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "8066ee7c49f9f29da96ee62f7cb13bee022cb4b68e51437b33da3b6d01398f13", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-buffer/4.1.94.Final/netty-buffer-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-buffer/4.1.94.Final/netty-buffer-4.1.94.Final.jar"}}, "io_netty_netty_codec_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "91243776ad68b4d8e39eafb9ec115e1b8fa9aecd147b12ef15bb691639498328", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-codec/4.1.94.Final/netty-codec-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-codec/4.1.94.Final/netty-codec-4.1.94.Final.jar"}}, "io_netty_netty_codec_http_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "1ada4580f68cd17a534fb3c0337087073223a76cb77304dbe5a1b19df3d53c2f", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-codec-http/4.1.94.Final/netty-codec-http-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-codec-http/4.1.94.Final/netty-codec-http-4.1.94.Final.jar"}}, "io_netty_netty_codec_http2_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "8fbd2e95abec6155b60ed3c9c1600ed4e17ffe3f053cd5a40677d879c0af961f", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-codec-http2/4.1.94.Final/netty-codec-http2-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-codec-http2/4.1.94.Final/netty-codec-http2-4.1.94.Final.jar"}}, "io_netty_netty_common_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "cb8d84a3e63aea90d0d7a333a02e50ac751d2b05db55745d981b5eff893f647b", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-common/4.1.94.Final/netty-common-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-common/4.1.94.Final/netty-common-4.1.94.Final.jar"}}, "io_netty_netty_handler_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "8e50719a9ab89e33ef85c5f36d780e0d7056b3f768b07d261d87baed7094eb3c", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-handler/4.1.94.Final/netty-handler-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-handler/4.1.94.Final/netty-handler-4.1.94.Final.jar"}}, "io_netty_netty_resolver_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "bd26e9bc5e94e2d3974a93fdf921658eff4f033bfd4c5208607760ab54298617", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-resolver/4.1.94.Final/netty-resolver-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-resolver/4.1.94.Final/netty-resolver-4.1.94.Final.jar"}}, "io_netty_netty_transport_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "a75afa84ca35a50225991b39e6b6278186e612f7a2a0c0e981de523aaac516a4", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-transport/4.1.94.Final/netty-transport-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-transport/4.1.94.Final/netty-transport-4.1.94.Final.jar"}}, "io_netty_netty_transport_classes_epoll_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "9d5d51eb42081d6fc13f4dca6855cd30d098a5b1d0b06d5644a1342bd1e50a44", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-transport-classes-epoll/4.1.94.Final/netty-transport-classes-epoll-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-transport-classes-epoll/4.1.94.Final/netty-transport-classes-epoll-4.1.94.Final.jar"}}, "io_netty_netty_transport_native_unix_common_4_1_94_Final": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "27d0dff1cd743190279becacfb372fe4d45b266edafad9f1c6c01b04d00280eb", "urls": ["https://repo1.maven.org/maven2/io/netty/netty-transport-native-unix-common/4.1.94.Final/netty-transport-native-unix-common-4.1.94.Final.jar"], "downloaded_file_path": "v1/io/netty/netty-transport-native-unix-common/4.1.94.Final/netty-transport-native-unix-common-4.1.94.Final.jar"}}, "io_opencensus_opencensus_api_0_31_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "f1474d47f4b6b001558ad27b952e35eda5cc7146788877fc52938c6eba24b382", "urls": ["https://repo1.maven.org/maven2/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar"], "downloaded_file_path": "v1/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar"}}, "io_opencensus_opencensus_contrib_http_util_0_31_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "3ea995b55a4068be22989b70cc29a4d788c2d328d1d50613a7a9afd13fdd2d0a", "urls": ["https://repo1.maven.org/maven2/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar"], "downloaded_file_path": "v1/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar"}}, "io_opencensus_opencensus_proto_0_2_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "0c192d451e9dd74e98721b27d02f0e2b6bca44b51563b5dabf2e211f7a3ebf13", "urls": ["https://repo1.maven.org/maven2/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar"], "downloaded_file_path": "v1/io/opencensus/opencensus-proto/0.2.0/opencensus-proto-0.2.0.jar"}}, "io_perfmark_perfmark_api_0_26_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b7d23e93a34537ce332708269a0d1404788a5b5e1949e82f5535fce51b3ea95b", "urls": ["https://repo1.maven.org/maven2/io/perfmark/perfmark-api/0.26.0/perfmark-api-0.26.0.jar"], "downloaded_file_path": "v1/io/perfmark/perfmark-api/0.26.0/perfmark-api-0.26.0.jar"}}, "javax_annotation_javax_annotation_api_1_3_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "e04ba5195bcd555dc95650f7cc614d151e4bcd52d29a10b8aa2197f3ab89ab9b", "urls": ["https://repo1.maven.org/maven2/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar"], "downloaded_file_path": "v1/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar"}}, "org_apache_commons_commons_lang3_3_12_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "d919d904486c037f8d193412da0c92e22a9fa24230b9d67a57855c5c31c7e94e", "urls": ["https://repo1.maven.org/maven2/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"], "downloaded_file_path": "v1/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar"}}, "org_apache_httpcomponents_httpclient_4_5_14": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "c8bc7e1c51a6d4ce72f40d2ebbabf1c4b68bfe76e732104b04381b493478e9d6", "urls": ["https://repo1.maven.org/maven2/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar"], "downloaded_file_path": "v1/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar"}}, "org_apache_httpcomponents_httpcore_4_4_16": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "6c9b3dd142a09dc468e23ad39aad6f75a0f2b85125104469f026e52a474e464f", "urls": ["https://repo1.maven.org/maven2/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar"], "downloaded_file_path": "v1/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar"}}, "org_apache_maven_maven_artifact_3_9_4": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "7dd352fd9f8ff86a1d0a7d89e6289d8d3cd346ac9b214ed85868d585be05ab78", "urls": ["https://repo1.maven.org/maven2/org/apache/maven/maven-artifact/3.9.4/maven-artifact-3.9.4.jar"], "downloaded_file_path": "v1/org/apache/maven/maven-artifact/3.9.4/maven-artifact-3.9.4.jar"}}, "org_checkerframework_checker_qual_3_33_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "e316255bbfcd9fe50d165314b85abb2b33cb2a66a93c491db648e498a82c2de1", "urls": ["https://repo1.maven.org/maven2/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar"], "downloaded_file_path": "v1/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar"}}, "org_codehaus_mojo_animal_sniffer_annotations_1_23": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "9ffe526bf43a6348e9d8b33b9cd6f580a7f5eed0cf055913007eda263de974d0", "urls": ["https://repo1.maven.org/maven2/org/codehaus/mojo/animal-sniffer-annotations/1.23/animal-sniffer-annotations-1.23.jar"], "downloaded_file_path": "v1/org/codehaus/mojo/animal-sniffer-annotations/1.23/animal-sniffer-annotations-1.23.jar"}}, "org_codehaus_plexus_plexus_utils_3_5_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "86e0255d4c879c61b4833ed7f13124e8bb679df47debb127326e7db7dd49a07b", "urls": ["https://repo1.maven.org/maven2/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.jar"], "downloaded_file_path": "v1/org/codehaus/plexus/plexus-utils/3.5.1/plexus-utils-3.5.1.jar"}}, "org_conscrypt_conscrypt_openjdk_uber_2_5_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "eaf537d98e033d0f0451cd1b8cc74e02d7b55ec882da63c88060d806ba89c348", "urls": ["https://repo1.maven.org/maven2/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar"], "downloaded_file_path": "v1/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar"}}, "org_reactivestreams_reactive_streams_1_0_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "1dee0481072d19c929b623e155e14d2f6085dc011529a0a0dbefc84cf571d865", "urls": ["https://repo1.maven.org/maven2/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar"], "downloaded_file_path": "v1/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar"}}, "org_slf4j_slf4j_api_1_7_30": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "cdba07964d1bb40a0761485c6b1e8c2f8fd9eb1d19c53928ac0d7f9510105c57", "urls": ["https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar"], "downloaded_file_path": "v1/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar"}}, "org_threeten_threetenbp_1_6_8": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "e4b1eb3d90c38a54c7f3384fda957e0b5bf0b41b40672a44ae8b03cb6c87ce06", "urls": ["https://repo1.maven.org/maven2/org/threeten/threetenbp/1.6.8/threetenbp-1.6.8.jar"], "downloaded_file_path": "v1/org/threeten/threetenbp/1.6.8/threetenbp-1.6.8.jar"}}, "software_amazon_awssdk_annotations_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "4eeddb1848a90c73b8ce85d7b556f0be36f0f97c780f1715b9cb59a93620eae2", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/annotations/2.20.128/annotations-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/annotations/2.20.128/annotations-2.20.128.jar"}}, "software_amazon_awssdk_apache_client_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b35142b110c70ba0fd79f6f3e7633701d98424bcecc70d92eb336cb830244a09", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/apache-client/2.20.128/apache-client-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/apache-client/2.20.128/apache-client-2.20.128.jar"}}, "software_amazon_awssdk_arns_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "db6e5c582aaafcbe2e1804090505c6dbd76188b2a1661ecfd06afb7e949985b9", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/arns/2.20.128/arns-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/arns/2.20.128/arns-2.20.128.jar"}}, "software_amazon_awssdk_auth_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "aa12cf67a51d28a6f486e4818e5f0bd2c1398135df6705dd020af1f28a2bafec", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/auth/2.20.128/auth-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/auth/2.20.128/auth-2.20.128.jar"}}, "software_amazon_awssdk_aws_core_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "105f5d4a204a6a759ab502922df4cd5aa2a6d1b0c5f53ce88713f60abd4650e9", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/aws-core/2.20.128/aws-core-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/aws-core/2.20.128/aws-core-2.20.128.jar"}}, "software_amazon_awssdk_aws_query_protocol_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "dddab4ee63ad1bbc42bfcb3a9085917983ff4b5db71bc60b7ba6c5c17cbe5256", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/aws-query-protocol/2.20.128/aws-query-protocol-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/aws-query-protocol/2.20.128/aws-query-protocol-2.20.128.jar"}}, "software_amazon_awssdk_aws_xml_protocol_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "085f9e55c26daa7d38b17795d0e767e159da595892b95a60a6be4e76936ea68f", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/aws-xml-protocol/2.20.128/aws-xml-protocol-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/aws-xml-protocol/2.20.128/aws-xml-protocol-2.20.128.jar"}}, "software_amazon_awssdk_crt_core_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "48d2b5c0102a234bf988da7e8ec5f36d51b41ae2b512df2cab29d99b6b7620eb", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/crt-core/2.20.128/crt-core-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/crt-core/2.20.128/crt-core-2.20.128.jar"}}, "software_amazon_awssdk_endpoints_spi_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "0b98f5553c1116520ef9022cebbde1b4dd7963c1c0f23b34137b64ccf17d0ff2", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/endpoints-spi/2.20.128/endpoints-spi-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/endpoints-spi/2.20.128/endpoints-spi-2.20.128.jar"}}, "software_amazon_awssdk_http_client_spi_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "b09f1e0392975093ba0a2231e7057b673dacf05a798fe1b3f1446ba4f32e6a9b", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/http-client-spi/2.20.128/http-client-spi-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/http-client-spi/2.20.128/http-client-spi-2.20.128.jar"}}, "software_amazon_awssdk_json_utils_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "82a05550dcf9538d878d9d26e8c97913aa34600f7614cd7fd3b6e1f3f67c13cd", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/json-utils/2.20.128/json-utils-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/json-utils/2.20.128/json-utils-2.20.128.jar"}}, "software_amazon_awssdk_metrics_spi_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "5fcbfe4d10d0814ea1caa963d66129b1dfcf5e2f7c3a8298596676985234f94c", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/metrics-spi/2.20.128/metrics-spi-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/metrics-spi/2.20.128/metrics-spi-2.20.128.jar"}}, "software_amazon_awssdk_netty_nio_client_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "d6117bf4c2f45c671e55ecdff60f364099ddc1cf9226c0c24601a7818b9a22ba", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/netty-nio-client/2.20.128/netty-nio-client-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/netty-nio-client/2.20.128/netty-nio-client-2.20.128.jar"}}, "software_amazon_awssdk_profiles_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "110a5a1bfa09b0be417d60bba97f9d8641d398ea36d72b942a97253066fd5fd0", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/profiles/2.20.128/profiles-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/profiles/2.20.128/profiles-2.20.128.jar"}}, "software_amazon_awssdk_protocol_core_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "59107235409e9af0ec2f68aaad0d6cfe78b79e23600a59081a3f2af83e81c3c2", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/protocol-core/2.20.128/protocol-core-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/protocol-core/2.20.128/protocol-core-2.20.128.jar"}}, "software_amazon_awssdk_regions_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "79ac0d6a19daf4b5cb480a955bc36ed083e728fd2d0fb78efde2bcaaed0fce9f", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/regions/2.20.128/regions-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/regions/2.20.128/regions-2.20.128.jar"}}, "software_amazon_awssdk_s3_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "9b8f061683e06703d5728f22379c31d39bcb1bdcb418e38957cdea886c2aea00", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/s3/2.20.128/s3-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/s3/2.20.128/s3-2.20.128.jar"}}, "software_amazon_awssdk_sdk_core_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "19fd1e07de476f6b6c8342e254bf9b7df723dee65ac34002547789ec070d6a99", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/sdk-core/2.20.128/sdk-core-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/sdk-core/2.20.128/sdk-core-2.20.128.jar"}}, "software_amazon_awssdk_third_party_jackson_core_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "5487638bb3033b4de5f9cc04d97c4b5ec48533f2617803818e6263edc58b37cc", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/third-party-jackson-core/2.20.128/third-party-jackson-core-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/third-party-jackson-core/2.20.128/third-party-jackson-core-2.20.128.jar"}}, "software_amazon_awssdk_utils_2_20_128": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "ba635695d0046fae35740e9e64da9f0e34dab7cbc9a64813ce9ab49ed989f948", "urls": ["https://repo1.maven.org/maven2/software/amazon/awssdk/utils/2.20.128/utils-2.20.128.jar"], "downloaded_file_path": "v1/software/amazon/awssdk/utils/2.20.128/utils-2.20.128.jar"}}, "software_amazon_eventstream_eventstream_1_0_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "0c37d8e696117f02c302191b8110b0d0eb20fa412fce34c3a269ec73c16ce822", "urls": ["https://repo1.maven.org/maven2/software/amazon/eventstream/eventstream/1.0.1/eventstream-1.0.1.jar"], "downloaded_file_path": "v1/software/amazon/eventstream/eventstream/1.0.1/eventstream-1.0.1.jar"}}, "rules_jvm_external_deps": {"bzlFile": "@@rules_jvm_external~//:coursier.bzl", "ruleClassName": "pinned_coursier_fetch", "attributes": {"user_provided_name": "rules_jvm_external_deps", "repositories": ["{ \"repo_url\": \"https://repo1.maven.org/maven2\" }"], "artifacts": ["{ \"group\": \"com.google.auth\", \"artifact\": \"google-auth-library-credentials\", \"version\": \"1.19.0\" }", "{ \"group\": \"com.google.auth\", \"artifact\": \"google-auth-library-oauth2-http\", \"version\": \"1.19.0\" }", "{ \"group\": \"com.google.cloud\", \"artifact\": \"google-cloud-core\", \"version\": \"2.22.0\" }", "{ \"group\": \"com.google.cloud\", \"artifact\": \"google-cloud-storage\", \"version\": \"2.26.1\" }", "{ \"group\": \"com.google.code.gson\", \"artifact\": \"gson\", \"version\": \"2.10.1\" }", "{ \"group\": \"com.google.googlejavaformat\", \"artifact\": \"google-java-format\", \"version\": \"1.17.0\" }", "{ \"group\": \"com.google.guava\", \"artifact\": \"guava\", \"version\": \"32.1.2-jre\" }", "{ \"group\": \"org.apache.maven\", \"artifact\": \"maven-artifact\", \"version\": \"3.9.4\" }", "{ \"group\": \"software.amazon.awssdk\", \"artifact\": \"s3\", \"version\": \"2.20.128\" }"], "fetch_sources": false, "fetch_javadoc": false, "generate_compat_repositories": false, "maven_install_json": "@@rules_jvm_external~//:rules_jvm_external_deps_install.json", "override_targets": {}, "strict_visibility": false, "strict_visibility_value": ["@@//visibility:private"], "additional_netrc_lines": [], "fail_if_repin_required": false, "use_starlark_android_rules": false, "aar_import_bzl_label": "@build_bazel_rules_android//android:rules.bzl", "duplicate_version_warning": "warn", "excluded_artifacts": [], "repin_instructions": ""}}, "maven_jar_migrator": {"bzlFile": "@@rules_jvm_external~//:coursier.bzl", "ruleClassName": "coursier_fetch", "attributes": {"user_provided_name": "maven_jar_migrator", "repositories": ["{ \"repo_url\": \"https://repo1.maven.org/maven2\" }"], "artifacts": ["{ \"group\": \"com.google.guava\", \"artifact\": \"guava\", \"version\": \"28.0-jre\" }"], "fail_on_missing_checksum": true, "fetch_sources": false, "fetch_javadoc": false, "excluded_artifacts": [], "generate_compat_repositories": false, "version_conflict_policy": "default", "override_targets": {}, "strict_visibility": false, "strict_visibility_value": ["@@//visibility:private"], "resolve_timeout": 600, "use_starlark_android_rules": false, "aar_import_bzl_label": "@build_bazel_rules_android//android:rules.bzl", "duplicate_version_warning": "warn", "ignore_empty_files": false}}, "protobuf_maven": {"bzlFile": "@@rules_jvm_external~//:coursier.bzl", "ruleClassName": "coursier_fetch", "attributes": {"user_provided_name": "protobuf_maven", "repositories": ["{ \"repo_url\": \"https://repo1.maven.org/maven2\" }", "{ \"repo_url\": \"https://repo.maven.apache.org/maven2\" }"], "artifacts": ["{ \"group\": \"com.google.caliper\", \"artifact\": \"caliper\", \"version\": \"1.0-beta-3\" }", "{ \"group\": \"com.google.code.findbugs\", \"artifact\": \"jsr305\", \"version\": \"3.0.2\" }", "{ \"group\": \"com.google.code.gson\", \"artifact\": \"gson\", \"version\": \"2.8.9\" }", "{ \"group\": \"com.google.errorprone\", \"artifact\": \"error_prone_annotations\", \"version\": \"2.5.1\" }", "{ \"group\": \"com.google.j2objc\", \"artifact\": \"j2objc-annotations\", \"version\": \"2.8\" }", "{ \"group\": \"com.google.guava\", \"artifact\": \"guava\", \"version\": \"32.0.1-jre\" }", "{ \"group\": \"com.google.guava\", \"artifact\": \"guava-testlib\", \"version\": \"32.0.1-jre\" }", "{ \"group\": \"com.google.truth\", \"artifact\": \"truth\", \"version\": \"1.1.2\" }", "{ \"group\": \"junit\", \"artifact\": \"junit\", \"version\": \"4.13.2\" }", "{ \"group\": \"org.mockito\", \"artifact\": \"mockito-core\", \"version\": \"4.3.1\" }", "{ \"group\": \"biz.aQute.bnd\", \"artifact\": \"biz.aQute.bndlib\", \"version\": \"6.4.0\" }", "{ \"group\": \"info.picocli\", \"artifact\": \"picocli\", \"version\": \"4.6.3\" }"], "fail_on_missing_checksum": true, "fetch_sources": false, "fetch_javadoc": false, "excluded_artifacts": [], "generate_compat_repositories": false, "version_conflict_policy": "default", "override_targets": {}, "strict_visibility": false, "strict_visibility_value": ["@@//visibility:private"], "resolve_timeout": 600, "use_starlark_android_rules": false, "aar_import_bzl_label": "@build_bazel_rules_android//android:rules.bzl", "duplicate_version_warning": "warn", "ignore_empty_files": false}}, "unpinned_stardoc_maven": {"bzlFile": "@@rules_jvm_external~//:coursier.bzl", "ruleClassName": "coursier_fetch", "attributes": {"user_provided_name": "stardoc_maven", "repositories": ["{ \"repo_url\": \"https://repo1.maven.org/maven2\" }"], "artifacts": ["{ \"group\": \"com.beust\", \"artifact\": \"jcommander\", \"version\": \"1.82\" }", "{ \"group\": \"com.google.escapevelocity\", \"artifact\": \"escapevelocity\", \"version\": \"1.1\" }", "{ \"group\": \"com.google.guava\", \"artifact\": \"guava\", \"version\": \"31.1-jre\" }", "{ \"group\": \"com.google.truth\", \"artifact\": \"truth\", \"version\": \"1.1.3\" }", "{ \"group\": \"junit\", \"artifact\": \"junit\", \"version\": \"4.13.2\" }"], "fail_on_missing_checksum": true, "fetch_sources": false, "fetch_javadoc": false, "excluded_artifacts": [], "generate_compat_repositories": false, "version_conflict_policy": "default", "override_targets": {}, "strict_visibility": true, "strict_visibility_value": ["@@//visibility:private"], "maven_install_json": "@@stardoc~//:maven_install.json", "resolve_timeout": 600, "use_starlark_android_rules": false, "aar_import_bzl_label": "@build_bazel_rules_android//android:rules.bzl", "duplicate_version_warning": "warn", "ignore_empty_files": false}}, "com_beust_jcommander_1_82": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "deeac157c8de6822878d85d0c7bc8467a19cc8484d37788f7804f039dde280b1", "urls": ["https://repo1.maven.org/maven2/com/beust/jcommander/1.82/jcommander-1.82.jar"], "downloaded_file_path": "v1/com/beust/jcommander/1.82/jcommander-1.82.jar"}}, "com_google_auto_value_auto_value_annotations_1_8_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "37ec09b47d7ed35a99d13927db5c86fc9071f620f943ead5d757144698310852", "urls": ["https://repo1.maven.org/maven2/com/google/auto/value/auto-value-annotations/1.8.1/auto-value-annotations-1.8.1.jar"], "downloaded_file_path": "v1/com/google/auto/value/auto-value-annotations/1.8.1/auto-value-annotations-1.8.1.jar"}}, "com_google_errorprone_error_prone_annotations_2_11_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "721cb91842b46fa056847d104d5225c8b8e1e8b62263b993051e1e5a0137b7ec", "urls": ["https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar"], "downloaded_file_path": "v1/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar"}}, "com_google_escapevelocity_escapevelocity_1_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "37e76e4466836dedb864fb82355cd01c3bd21325ab642d89a0f759291b171231", "urls": ["https://repo1.maven.org/maven2/com/google/escapevelocity/escapevelocity/1.1/escapevelocity-1.1.jar"], "downloaded_file_path": "v1/com/google/escapevelocity/escapevelocity/1.1/escapevelocity-1.1.jar"}}, "com_google_guava_guava_31_1_jre": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "a42edc9cab792e39fe39bb94f3fca655ed157ff87a8af78e1d6ba5b07c4a00ab", "urls": ["https://repo1.maven.org/maven2/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar"], "downloaded_file_path": "v1/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar"}}, "com_google_j2objc_j2objc_annotations_1_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "21af30c92267bd6122c0e0b4d20cccb6641a37eaf956c6540ec471d584e64a7b", "urls": ["https://repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar"], "downloaded_file_path": "v1/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar"}}, "com_google_truth_truth_1_1_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "fc0b67782289a2aabfddfdf99eff1dcd5edc890d49143fcd489214b107b8f4f3", "urls": ["https://repo1.maven.org/maven2/com/google/truth/truth/1.1.3/truth-1.1.3.jar"], "downloaded_file_path": "v1/com/google/truth/truth/1.1.3/truth-1.1.3.jar"}}, "junit_junit_4_13_2": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "8e495b634469d64fb8acfa3495a065cbacc8a0fff55ce1e31007be4c16dc57d3", "urls": ["https://repo1.maven.org/maven2/junit/junit/4.13.2/junit-4.13.2.jar"], "downloaded_file_path": "v1/junit/junit/4.13.2/junit-4.13.2.jar"}}, "org_checkerframework_checker_qual_3_13_0": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "3ea0dcd73b4d6cb2fb34bd7ed4dad6db327a01ebad7db05eb7894076b3d64491", "urls": ["https://repo1.maven.org/maven2/org/checkerframework/checker-qual/3.13.0/checker-qual-3.13.0.jar"], "downloaded_file_path": "v1/org/checkerframework/checker-qual/3.13.0/checker-qual-3.13.0.jar"}}, "org_hamcrest_hamcrest_core_1_3": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "66fdef91e9739348df7a096aa384a5685f4e875584cce89386a7a47251c4d8e9", "urls": ["https://repo1.maven.org/maven2/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"], "downloaded_file_path": "v1/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar"}}, "org_ow2_asm_asm_9_1": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "cda4de455fab48ff0bcb7c48b4639447d4de859a7afc30a094a986f0936beba2", "urls": ["https://repo1.maven.org/maven2/org/ow2/asm/asm/9.1/asm-9.1.jar"], "downloaded_file_path": "v1/org/ow2/asm/asm/9.1/asm-9.1.jar"}}, "stardoc_maven": {"bzlFile": "@@rules_jvm_external~//:coursier.bzl", "ruleClassName": "pinned_coursier_fetch", "attributes": {"user_provided_name": "stardoc_maven", "repositories": ["{ \"repo_url\": \"https://repo1.maven.org/maven2\" }"], "artifacts": ["{ \"group\": \"com.beust\", \"artifact\": \"jcommander\", \"version\": \"1.82\" }", "{ \"group\": \"com.google.escapevelocity\", \"artifact\": \"escapevelocity\", \"version\": \"1.1\" }", "{ \"group\": \"com.google.guava\", \"artifact\": \"guava\", \"version\": \"31.1-jre\" }", "{ \"group\": \"com.google.truth\", \"artifact\": \"truth\", \"version\": \"1.1.3\" }", "{ \"group\": \"junit\", \"artifact\": \"junit\", \"version\": \"4.13.2\" }"], "fetch_sources": false, "fetch_javadoc": false, "generate_compat_repositories": false, "maven_install_json": "@@stardoc~//:maven_install.json", "override_targets": {}, "strict_visibility": true, "strict_visibility_value": ["@@//visibility:private"], "additional_netrc_lines": [], "fail_if_repin_required": true, "use_starlark_android_rules": false, "aar_import_bzl_label": "@build_bazel_rules_android//android:rules.bzl", "duplicate_version_warning": "warn", "excluded_artifacts": [], "repin_instructions": ""}}}, "recordedRepoMappingEntries": [["rules_jvm_external~", "bazel_tools", "bazel_tools"]]}}, "@@rules_kotlin~//src/main/starlark/core/repositories:bzlmod_setup.bzl%rules_kotlin_extensions": {"general": {"bzlTransitiveDigest": "fus14IFJ/1LGWWGKPH/U18VnJCoMjfDt1ckahqCnM0A=", "usagesDigest": "aJF6fLy82rR95Ff5CZPAqxNoFgOMLMN5ImfBS0nhnkg=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_github_jetbrains_kotlin_git": {"bzlFile": "@@rules_kotlin~//src/main/starlark/core/repositories:compiler.bzl", "ruleClassName": "kotlin_compiler_git_repository", "attributes": {"urls": ["https://github.com/JetBrains/kotlin/releases/download/v1.9.23/kotlin-compiler-1.9.23.zip"], "sha256": "93137d3aab9afa9b27cb06a824c2324195c6b6f6179d8a8653f440f5bd58be88"}}, "com_github_jetbrains_kotlin": {"bzlFile": "@@rules_kotlin~//src/main/starlark/core/repositories:compiler.bzl", "ruleClassName": "kotlin_capabilities_repository", "attributes": {"git_repository_name": "com_github_jetbrains_kotlin_git", "compiler_version": "1.9.23"}}, "com_github_google_ksp": {"bzlFile": "@@rules_kotlin~//src/main/starlark/core/repositories:ksp.bzl", "ruleClassName": "ksp_compiler_plugin_repository", "attributes": {"urls": ["https://github.com/google/ksp/releases/download/1.9.23-1.0.20/artifacts.zip"], "sha256": "ee0618755913ef7fd6511288a232e8fad24838b9af6ea73972a76e81053c8c2d", "strip_version": "1.9.23-1.0.20"}}, "com_github_pinterest_ktlint": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_file", "attributes": {"sha256": "01b2e0ef893383a50dbeb13970fe7fa3be36ca3e83259e01649945b09d736985", "urls": ["https://github.com/pinterest/ktlint/releases/download/1.3.0/ktlint"], "executable": true}}, "rules_android": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"sha256": "cd06d15dd8bb59926e4d65f9003bfc20f9da4b2519985c27e190cddc8b7a7806", "strip_prefix": "rules_android-0.1.1", "urls": ["https://github.com/bazelbuild/rules_android/archive/v0.1.1.zip"]}}}, "recordedRepoMappingEntries": [["rules_kotlin~", "bazel_tools", "bazel_tools"]]}}, "@@rules_python~//python/extensions:python.bzl%python": {"general": {"bzlTransitiveDigest": "0JWOeqcilHRNkAm8MpTU0k3/CQv6aMPwWnUDE2EnX/w=", "usagesDigest": "8A1HPRx/xfVolg+D7GvLukYU1Te2ZTCrI0stuAEJk2E=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {"RULES_PYTHON_BZLMOD_DEBUG": null}, "generatedRepoSpecs": {"python_3_8_aarch64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "eae09ed83ee66353c0cee435ea2d3e4868bd0537214803fb256a1a2928710bc0", "patches": [], "platform": "aarch64-apple-darwin", "python_version": "3.8.19", "release_filename": "20240415/cpython-3.8.19+20240415-aarch64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.8.19+20240415-aarch64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_8_aarch64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "5bde36c53a9a511a1618f159abed77264392eb054edeb57bb5740f6335db34a3", "patches": [], "platform": "aarch64-unknown-linux-gnu", "python_version": "3.8.19", "release_filename": "20240415/cpython-3.8.19+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.8.19+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_8_x86_64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "05f0c488d84f7590afb6f5d192f071df80584339dda581b6186effc6cd690f6b", "patches": [], "platform": "x86_64-apple-darwin", "python_version": "3.8.19", "release_filename": "20240415/cpython-3.8.19+20240415-x86_64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.8.19+20240415-x86_64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_8_x86_64-pc-windows-msvc": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "ee95c27e5d9de165e77c280ad4d7b51b0dab9567e7e233fc3acf72363870a168", "patches": [], "platform": "x86_64-pc-windows-msvc", "python_version": "3.8.19", "release_filename": "20240415/cpython-3.8.19+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.8.19+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_8_x86_64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "b33feb5ce0d7f9c4aca8621a9d231dfd9d2f6e26eccb56b63f07041ff573d5a5", "patches": [], "platform": "x86_64-unknown-linux-gnu", "python_version": "3.8.19", "release_filename": "20240415/cpython-3.8.19+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.8.19+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_8_host": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "host_toolchain", "attributes": {"python_version": "3.8.19", "user_repository_name": "python_3_8", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_8": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "toolchain_aliases", "attributes": {"python_version": "3.8.19", "user_repository_name": "python_3_8", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_9_aarch64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "2671bb4ffd036f03076c8aa41e3828c4c16a602e93e2249a8e7b28fd83fdde51", "patches": [], "platform": "aarch64-apple-darwin", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-aarch64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-aarch64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_aarch64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "b18ad819f04c5b2cff6ffa95dd59263d00dcd6f5633d11e43685b4017469cb1c", "patches": [], "platform": "aarch64-unknown-linux-gnu", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_ppc64le-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "2521ebe9eef273ab718670ed6c6c11760214cdc2e34b7609674179629659a6cd", "patches": [], "platform": "ppc64le-unknown-linux-gnu", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_s390x-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "8f83b8f357031cd6788ca253b1ac29020b73c8b41d0e5fb09a554d0d6c04ae83", "patches": [], "platform": "s390x-unknown-linux-gnu", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-s390x-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-s390x-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_x86_64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "627d903588c0e69ed8b941ba9f91e070e38105a627c5b8c730267744760dca84", "patches": [], "platform": "x86_64-apple-darwin", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-x86_64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-x86_64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_x86_64-pc-windows-msvc": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "9b46faee13e37d8bfa4c02de3775ca3d5dec9378697d755b750fd37788179286", "patches": [], "platform": "x86_64-pc-windows-msvc", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_x86_64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "00f698873804863dedc0e2b2c2cc4303b49ab0703af2e5883e11340cb8079d0f", "patches": [], "platform": "x86_64-unknown-linux-gnu", "python_version": "3.9.19", "release_filename": "20240415/cpython-3.9.19+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.9.19+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_9_host": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "host_toolchain", "attributes": {"python_version": "3.9.19", "user_repository_name": "python_3_9", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_9": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "toolchain_aliases", "attributes": {"python_version": "3.9.19", "user_repository_name": "python_3_9", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_10_aarch64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "389da793b7666e9310908b4fe3ddcf0a20b55727fcb384c7c49b01bb21716f89", "patches": [], "platform": "aarch64-apple-darwin", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-aarch64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-aarch64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_aarch64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "2f9f26c430df19d6d2a25ac3f2a8e74106d32b9951b85f95218ceeb13d52e952", "patches": [], "platform": "aarch64-unknown-linux-gnu", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_ppc64le-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "9f178c19850567391188c2f9de87ce3c9fce698a23f5f3470be03745a03d1daa", "patches": [], "platform": "ppc64le-unknown-linux-gnu", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_s390x-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "648aa520de74ee426231e4a5349598990abe42a97c347ce6240b166f23ee5903", "patches": [], "platform": "s390x-unknown-linux-gnu", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-s390x-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-s390x-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_x86_64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "8e27ec6f27b3a27be892c7a9db1e278c858acd9d90c1114013fe5587cd6fc5e6", "patches": [], "platform": "x86_64-apple-darwin", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-x86_64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-x86_64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_x86_64-pc-windows-msvc": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "186b5632fb2fa5b5e6eee4110ce9bbb0349f52bb2163d2a1f5188b1d8eb1b5f3", "patches": [], "platform": "x86_64-pc-windows-msvc", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_x86_64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "c83c5485659250ef4e4fedb8e7f7b97bc99cc8cf5a1b11d0d1a98d347a43411d", "patches": [], "platform": "x86_64-unknown-linux-gnu", "python_version": "3.10.14", "release_filename": "20240415/cpython-3.10.14+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.10.14+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_10_host": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "host_toolchain", "attributes": {"python_version": "3.10.14", "user_repository_name": "python_3_10", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_10": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "toolchain_aliases", "attributes": {"python_version": "3.10.14", "user_repository_name": "python_3_10", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_11_aarch64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "7af7058f7c268b4d87ed7e08c2c7844ef8460863b3e679db3afdce8bb1eedfae", "patches": [], "platform": "aarch64-apple-darwin", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-aarch64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-aarch64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_aarch64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "b3a7199ac2615d75fb906e5ba556432efcf24baf8651fc70370d9f052d4069ee", "patches": [], "platform": "aarch64-unknown-linux-gnu", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_ppc64le-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "03f62d1e2d400c9662cdd12ae33a6f328c34ae8e2b872f8563a144834742bd6a", "patches": [], "platform": "ppc64le-unknown-linux-gnu", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_s390x-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "3f7a0dd64fa292977c4da09e865ee504a48e55dbc2dbfd9ff4b991af891e4446", "patches": [], "platform": "s390x-unknown-linux-gnu", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-s390x-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-s390x-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_x86_64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "9afd734f63a23783cf0257bef25c9231ffc80e7747486dc54cf72f325213fd15", "patches": [], "platform": "x86_64-apple-darwin", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-x86_64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_x86_64-pc-windows-msvc": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "368474c69f476e7de4adaf50b61d9fcf6ec8b4db88cc43c5f71c860b3cd29c69", "patches": [], "platform": "x86_64-pc-windows-msvc", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_x86_64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "78b1c16a9fd032997ba92a60f46a64f795cd18ff335659dfdf6096df277b24d5", "patches": [], "platform": "x86_64-unknown-linux-gnu", "python_version": "3.11.9", "release_filename": "20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.11.9+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_11_host": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "host_toolchain", "attributes": {"python_version": "3.11.9", "user_repository_name": "python_3_11", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_11": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "toolchain_aliases", "attributes": {"python_version": "3.11.9", "user_repository_name": "python_3_11", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_12_aarch64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "ccc40e5af329ef2af81350db2a88bbd6c17b56676e82d62048c15d548401519e", "patches": [], "platform": "aarch64-apple-darwin", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-aarch64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-aarch64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_aarch64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "ec8126de97945e629cca9aedc80a29c4ae2992c9d69f2655e27ae73906ba187d", "patches": [], "platform": "aarch64-unknown-linux-gnu", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-aarch64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_ppc64le-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "c5dcf08b8077e617d949bda23027c49712f583120b3ed744f9b143da1d580572", "patches": [], "platform": "ppc64le-unknown-linux-gnu", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-ppc64le-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_s390x-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "872fc321363b8cdd826fd2cb1adfd1ceb813bc1281f9d410c1c2c4e177e8df86", "patches": [], "platform": "s390x-unknown-linux-gnu", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-s390x-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-s390x-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_x86_64-apple-darwin": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "c37a22fca8f57d4471e3708de6d13097668c5f160067f264bb2b18f524c890c8", "patches": [], "platform": "x86_64-apple-darwin", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-x86_64-apple-darwin-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-x86_64-apple-darwin-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_x86_64-pc-windows-msvc": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "f7cfa4ad072feb4578c8afca5ba9a54ad591d665a441dd0d63aa366edbe19279", "patches": [], "platform": "x86_64-pc-windows-msvc", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-x86_64-pc-windows-msvc-shared-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_x86_64-unknown-linux-gnu": {"bzlFile": "@@rules_python~//python:repositories.bzl", "ruleClassName": "python_repository", "attributes": {"sha256": "a73ba777b5d55ca89edef709e6b8521e3f3d4289581f174c8699adfb608d09d6", "patches": [], "platform": "x86_64-unknown-linux-gnu", "python_version": "3.12.3", "release_filename": "20240415/cpython-3.12.3+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz", "urls": ["https://github.com/indygreg/python-build-standalone/releases/download/20240415/cpython-3.12.3+20240415-x86_64-unknown-linux-gnu-install_only.tar.gz"], "distutils_content": "", "strip_prefix": "python", "coverage_tool": "", "ignore_root_user_error": false}}, "python_3_12_host": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "host_toolchain", "attributes": {"python_version": "3.12.3", "user_repository_name": "python_3_12", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "python_3_12": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "toolchain_aliases", "attributes": {"python_version": "3.12.3", "user_repository_name": "python_3_12", "platforms": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "ppc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "pythons_hub": {"bzlFile": "@@rules_python~//python/private/bzlmod:pythons_hub.bzl", "ruleClassName": "hub_repo", "attributes": {"default_python_version": "3.11", "toolchain_prefixes": ["_0000_python_3_8_", "_0001_python_3_9_", "_0002_python_3_10_", "_0003_python_3_12_", "_0004_python_3_11_"], "toolchain_python_versions": ["3.8", "3.9", "3.10", "3.12", "3.11"], "toolchain_set_python_version_constraints": ["True", "True", "True", "True", "False"], "toolchain_user_repository_names": ["python_3_8", "python_3_9", "python_3_10", "python_3_12", "python_3_11"]}}, "python_versions": {"bzlFile": "@@rules_python~//python/private:toolchains_repo.bzl", "ruleClassName": "multi_toolchain_aliases", "attributes": {"python_versions": {"3.8": "python_3_8", "3.9": "python_3_9", "3.10": "python_3_10", "3.11": "python_3_11", "3.12": "python_3_12"}}}}, "recordedRepoMappingEntries": [["rules_python~", "bazel_skylib", "bazel_skylib~"], ["rules_python~", "bazel_tools", "bazel_tools"]]}}, "@@rules_python~//python/private/bzlmod:internal_deps.bzl%internal_deps": {"general": {"bzlTransitiveDigest": "FIxBv6bj8JA15KMRKbE56cq56ExFrmBRQz4nBIaIzgQ=", "usagesDigest": "fhMm4xjMIOaOwEl58svQ0Uht/1dctBL1itbhbscVZQA=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"rules_python_internal": {"bzlFile": "@@rules_python~//python/private:internal_config_repo.bzl", "ruleClassName": "internal_config_repo", "attributes": {}}, "pypi__build": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/e2/03/f3c8ba0a6b6e30d7d18c40faab90807c9bb5e9a1e3b2fe2008af624a9c97/build-1.2.1-py3-none-any.whl", "sha256": "75e10f767a433d9a86e50d83f418e83efc18ede923ee5ff7df93b6cb0306c5d4", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__click": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/00/2e/d53fa4befbf2cfa713304affc7ca780ce4fc1fd8710527771b58311a3229/click-8.1.7-py3-none-any.whl", "sha256": "ae74fb96c20a0277a1d615f1e4d73c8414f5a98db8b799a7931d1582f3390c28", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__colorama": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", "sha256": "4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__importlib_metadata": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/cc/37/db7ba97e676af155f5fcb1a35466f446eadc9104e25b83366e8088c9c926/importlib_metadata-6.8.0-py3-none-any.whl", "sha256": "3ebb78df84a805d7698245025b975d9d67053cd94c79245ba4b3eb694abe68bb", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__installer": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/e5/ca/1172b6638d52f2d6caa2dd262ec4c811ba59eee96d54a7701930726bce18/installer-0.7.0-py3-none-any.whl", "sha256": "05d1933f0a5ba7d8d6296bb6d5018e7c94fa473ceb10cf198a92ccea19c27b53", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__more_itertools": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/5a/cb/6dce742ea14e47d6f565589e859ad225f2a5de576d7696e0623b784e226b/more_itertools-10.1.0-py3-none-any.whl", "sha256": "64e0735fcfdc6f3464ea133afe8ea4483b1c5fe3a3d69852e6503b43a0b222e6", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__packaging": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/ab/c3/57f0601a2d4fe15de7a553c00adbc901425661bf048f2a22dfc500caf121/packaging-23.1-py3-none-any.whl", "sha256": "994793af429502c4ea2ebf6bf664629d07c1a9fe974af92966e4b8d2df7edc61", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__pep517": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/ee/2f/ef63e64e9429111e73d3d6cbee80591672d16f2725e648ebc52096f3d323/pep517-0.13.0-py3-none-any.whl", "sha256": "4ba4446d80aed5b5eac6509ade100bff3e7943a8489de249654a5ae9b33ee35b", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__pip": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/50/c2/e06851e8cc28dcad7c155f4753da8833ac06a5c704c109313b8d5a62968a/pip-23.2.1-py3-none-any.whl", "sha256": "7ccf472345f20d35bdc9d1841ff5f313260c2c33fe417f48c30ac46cccabf5be", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__pip_tools": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/0d/dc/38f4ce065e92c66f058ea7a368a9c5de4e702272b479c0992059f7693941/pip_tools-7.4.1-py3-none-any.whl", "sha256": "4c690e5fbae2f21e87843e89c26191f0d9454f362d8acdbd695716493ec8b3a9", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__pyproject_hooks": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/d5/ea/9ae603de7fbb3df820b23a70f6aff92bf8c7770043254ad8d2dc9d6bcba4/pyproject_hooks-1.0.0-py3-none-any.whl", "sha256": "283c11acd6b928d2f6a7c73fa0d01cb2bdc5f07c57a2eeb6e83d5e56b97976f8", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__setuptools": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/4f/ab/0bcfebdfc3bfa8554b2b2c97a555569c4c1ebc74ea288741ea8326c51906/setuptools-68.1.2-py3-none-any.whl", "sha256": "3d8083eed2d13afc9426f227b24fd1659489ec107c0e86cec2ffdde5c92e790b", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__tomli": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/97/75/10a9ebee3fd790d20926a90a2547f0bf78f371b2f13aa822c759680ca7b9/tomli-2.0.1-py3-none-any.whl", "sha256": "939de3e7a6161af0c887ef91b7d41a53e7c5a1ca976325f429cb46ea9bc30ecc", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__wheel": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/b8/8b/31273bf66016be6ad22bb7345c37ff350276cfd46e389a0c2ac5da9d9073/wheel-0.41.2-py3-none-any.whl", "sha256": "75909db2664838d015e3d9139004ee16711748a52c8f336b52882266540215d8", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}, "pypi__zipp": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"url": "https://files.pythonhosted.org/packages/8c/08/d3006317aefe25ea79d3b76c9650afabaf6d63d1c8443b236e7405447503/zipp-3.16.2-py3-none-any.whl", "sha256": "679e51dd4403591b2d6838a48de3d283f3d188412a9782faadf845f298736ba0", "type": "zip", "build_file_content": "package(default_visibility = [\"//visibility:public\"])\n\nload(\"@rules_python//python:defs.bzl\", \"py_library\")\n\npy_library(\n    name = \"lib\",\n    srcs = glob([\"**/*.py\"]),\n    data = glob([\"**/*\"], exclude=[\n        # These entries include those put into user-installed dependencies by\n        # data_exclude in /python/pip_install/tools/bazel.py\n        # to avoid non-determinism following pip install's behavior.\n        \"**/*.py\",\n        \"**/*.pyc\",\n        \"**/*.pyc.*\",  # During pyc creation, temp files named *.pyc.NNN are created\n        \"**/* *\",\n        \"**/*.dist-info/RECORD\",\n        \"BUILD\",\n        \"WORKSPACE\",\n    ]),\n    # This makes this directory a top-level in the python import\n    # search path for anything that depends on this.\n    imports = [\".\"],\n)\n"}}}, "recordedRepoMappingEntries": [["rules_python~", "bazel_tools", "bazel_tools"]]}}, "@@rules_shellcheck~//internal:extensions.bzl%shellcheck_dependencies": {"general": {"bzlTransitiveDigest": "xCx5X4m8e6Bpk4mX1e2bP8BlPm9seiXKDEMJZtJzG8k=", "usagesDigest": "QQq5PkNMTCO+s+NGwJVwibcCSJalxaRZuBTUZ9YWuYw=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"shellcheck_darwin_aarch64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"strip_prefix": "shellcheck-v0.11.0", "build_file_content": "exports_files([\"shellcheck\"])\n", "sha256": "56affdd8de5527894dca6dc3d7e0a99a873b0f004d7aabc30ae407d3f48b0a79", "urls": ["https://github.com/koalaman/shellcheck/releases/download/v0.11.0/shellcheck-v0.11.0.darwin.aarch64.tar.xz"]}}, "shellcheck_darwin_x86_64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"strip_prefix": "shellcheck-v0.11.0", "build_file_content": "exports_files([\"shellcheck\"])\n", "sha256": "3c89db4edcab7cf1c27bff178882e0f6f27f7afdf54e859fa041fca10febe4c6", "urls": ["https://github.com/koalaman/shellcheck/releases/download/v0.11.0/shellcheck-v0.11.0.darwin.x86_64.tar.xz"]}}, "shellcheck_linux_aarch64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"strip_prefix": "shellcheck-v0.11.0", "build_file_content": "exports_files([\"shellcheck\"])\n", "sha256": "12b331c1d2db6b9eb13cfca64306b1b157a86eb69db83023e261eaa7e7c14588", "urls": ["https://github.com/koalaman/shellcheck/releases/download/v0.11.0/shellcheck-v0.11.0.linux.aarch64.tar.xz"]}}, "shellcheck_linux_armv6hf": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"strip_prefix": "shellcheck-v0.11.0", "build_file_content": "exports_files([\"shellcheck\"])\n", "sha256": "8afc50b302d5feeac9381ea114d563f0150d061520042b254d6eb715797c8223", "urls": ["https://github.com/koalaman/shellcheck/releases/download/v0.11.0/shellcheck-v0.11.0.linux.armv6hf.tar.xz"]}}, "shellcheck_linux_x86_64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"strip_prefix": "shellcheck-v0.11.0", "build_file_content": "exports_files([\"shellcheck\"])\n", "sha256": "8c3be12b05d5c177a04c29e3c78ce89ac86f1595681cab149b65b97c4e227198", "urls": ["https://github.com/koalaman/shellcheck/releases/download/v0.11.0/shellcheck-v0.11.0.linux.x86_64.tar.xz"]}}, "shellcheck_windows_x86_64": {"bzlFile": "@@bazel_tools//tools/build_defs/repo:http.bzl", "ruleClassName": "http_archive", "attributes": {"build_file_content": "exports_files([\"shellcheck\"])\n", "sha256": "8a4e35ab0b331c85d73567b12f2a444df187f483e5079ceffa6bda1faa2e740e", "urls": ["https://github.com/koalaman/shellcheck/releases/download/v0.11.0/shellcheck-v0.11.0.zip"]}}}, "recordedRepoMappingEntries": [["rules_shellcheck~", "bazel_tools", "bazel_tools"], ["rules_shellcheck~", "rules_shellcheck", "rules_shellcheck~"]]}}}}